version: '3.8'

volumes:
  db_storage:
  n8n_storage:

networks:
  n8n_network:
    driver: bridge

services:
  postgres:
    image: postgres:16
    restart: always
    environment:
      - POSTGRES_USER
      - POSTGRES_PASSWORD
      - POSTGRES_DB
      - POSTGRES_NON_ROOT_USER
      - POSTGRES_NON_ROOT_PASSWORD
    volumes:
      - db_storage:/var/lib/postgresql/data
      - ./init-data.sh:/docker-entrypoint-initdb.d/init-data.sh
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -h localhost -U ${POSTGRES_USER} -d ${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - n8n_network

  n8n:
    #image: docker.n8n.io/n8nio/n8n
    build: 
      context: .
      dockerfile: Dockerfile
    restart: always
    environment:
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=${POSTGRES_DB}
      - DB_POSTGRESDB_USER=${POSTGRES_NON_ROOT_USER}
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_NON_ROOT_PASSWORD}
      - N8N_SECURE_COOKIE=false
      - NODE_FUNCTION_ALLOW_EXTERNAL=${NODE_FUNCTION_ALLOW_EXTERNAL}  
      - NODE_FUNCTION_ALLOW_BUILTIN=fs 
      - N8N_HOST=${HOST_IP}
      - WEBHOOK_URL=http://${HOST_IP}:5678/
    ports:
      - "0.0.0.0:5678:5678"
    links:
      - postgres
    volumes:
      - n8n_storage:/home/<USER>/.n8n
      - ./internal:/app/internal
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - n8n_network

  filebrowser:
    image: filebrowser/filebrowser:v2.32.0
    container_name: filebrowser
    volumes:
      - ./internal:/srv 
      - ./filebrowser/filebrowser.db:/database/filebrowser.db
      - ./filebrowser/settings.json:/config/settings.json
    environment:
      - PUID=$(id -u)
      - PGID=$(id -g)
    ports:
      - 8095:80
    networks:
      - n8n_network
  
  crawl4ai:
    image: unclecode/crawl4ai:0.6.0-r2
    environment:
      - MAX_CONCURRENT_TASKS=5
    ports:
      - "11235:11235"
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - n8n_network

# Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: n8n-frontend
    ports:
      - "3000:3000"  # React dev server with hot reload
      - "8081:80"    # Express API server (map external 8081 to internal 80)
    environment:
      - N8N_WEBHOOK_URL=http://n8n:5678/webhook/journal
      - N8N_WEBHOOK_TEST_URL=http://n8n:5678/webhook-test/journal
      - PROGRESS_WS_URL=ws://localhost:80/progress  # WebSocket URL for progress monitoring
      - HOST_IP=${HOST_IP:-localhost}  # Host IP for browser access
      - PORT=3000     # React dev server port
      - API_PORT=80   # Express API server port
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true  # Enable polling for hot reload in Docker
    volumes:
      - ./frontend:/app  # Mount source code for hot reloading
      - /app/node_modules  # Prevent overwriting node_modules
    depends_on:
      - n8n
    networks:
      - n8n_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
