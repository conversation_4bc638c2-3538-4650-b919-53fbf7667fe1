import async<PERSON>
from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlerRunConfig
from crawl4ai.deep_crawling import BFSDeep<PERSON>rawlStrategy
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.content_filter_strategy import <PERSON><PERSON>25<PERSON>ontentFilter
from crawl4ai.deep_crawling.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>, ContentRelevanceFilter
from crawl4ai.content_filter_strategy import PruningC<PERSON>nt<PERSON>ilter

async def main():
    bm25_filter = BM25ContentFilter(
        user_query="machine learning",
        bm25_threshold=1.2,
    )

    prune_filter = PruningContentFilter(
        threshold=0.5,
        threshold_type="fixed",  # or "dynamic"
        min_word_threshold=50
    )

    md_generator = DefaultMarkdownGenerator(
        content_filter=prune_filter,
        options={"ignore_links": True},
    )

    relevance_filter = ContentRelevanceFilter(
    query="Web crawling and data extraction with Python",
    threshold=0.7  # Minimum similarity score (0.0 to 1.0)
)

    # Configure a deep crawl up to depth 2, text only, exclude scripts/styles
    config = CrawlerRunConfig(
        deep_crawl_strategy=BFSDeepCrawlStrategy(
            max_depth=2,           # Set your desired depth here
            max_pages=20,
            filter_chain=FilterChain([relevance_filter]),
            include_external=False # Stay within the same domain
        ),
        only_text=True,            # Extract text only
        excluded_tags=["script", "style"],  # Remove scripts and styles
        simulate_user=True,
        magic=True,
        wait_until="networkidle",  # Wait until network is idle
        page_timeout=60000,
        markdown_generator=md_generator,
        remove_overlay_elements=True,
        remove_forms=True,
    )

    print(config.dump())

    # async with AsyncWebCrawler() as crawler:
    #     results = await crawler.arun("https://lolesports.com/en-US/news", config=config)
    #     for result in results:
    #         print(f"URL: {result.url}")
    #         print(f"Depth: {result.metadata.get('depth', 0)}")
    #         print(f"Text: {result.text[:500]}...")  # Print first 500 chars

if __name__ == "__main__":
    asyncio.run(main())