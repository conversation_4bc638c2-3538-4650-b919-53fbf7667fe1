{"name": "Scrape & Summarize (Multiple Terms)", "nodes": [{"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"Topic\": \"topicHere\",\n  \"Distribution\": [],\n  \"searchTerms\": [],\n  \"Industries\": [],\n  \"progressStart\": 0,\n  \"progressEnd\": 50,\n  \"searchDepth\": 1,\n  \"maxPages\": 5\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1280, 60], "id": "47ef1b45-cf5f-4b55-9529-b8705c66484f", "name": "When Executed by Another Workflow"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [828, -80], "id": "a4cbcc5e-a6ea-491e-b51a-ccd2e7942706", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "toEdGFaHKWeU2Izy", "name": "OpenAi account"}}}, {"parameters": {"fieldToSplitOut": "['searchTerms']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-600, 60], "id": "eb2bd1f8-3a60-4bb4-a157-e58d6df9f5c8", "name": "Split Out1"}, {"parameters": {"jsCode": "const totalItems = $input.all().length;\n\nreturn {\n  \"numItems\": totalItems\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-400, -80], "id": "68b6d0b7-2345-4127-aed4-3b2d9623719b", "name": "Count <PERSON>"}, {"parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-200, 40], "id": "899e55c5-43a1-45c7-ad7a-7e69f328ca6c", "name": "Wait for Count <PERSON>em"}, {"parameters": {"batchSize": "=1", "options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [60, 40], "id": "6dfc85fa-f4e6-4068-8612-99755b28fe17", "name": "Loop Over Items"}, {"parameters": {"jsCode": "// Start\nconst progressStart = $('Unified Input').first().json.progressStart\n// End\nconst progressEnd = $('Unified Input').first().json.progressEnd\n\n// Num items\nconst numItems = $('Count Items').first().json.numItems\n\n// Calculate 'step' based on progress start and end\nconst step = (progressEnd - progressStart) / numItems\nconst progressUpdated = progressStart + ($runIndex * step)\n\nreturn {\n  \"success\": $input.first().json.success,\n  \"clientCount\": $input.first().json.clientCount,\n  \"progress\": Math.floor(progressUpdated)\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 20], "id": "6bc551c9-4bcb-49ee-b92a-048d429a1b5b", "name": "Increment Progress"}, {"parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1388, 32], "id": "ec6af5b2-9710-4279-9c98-e47c4823d10e", "name": "Wait for branches to complete"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "text"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [900, 460], "id": "d212a2ed-3c78-4d1d-ba58-511c2f75bcdc", "name": "Aggregate1"}, {"parameters": {"batchSize": "={{ $('Unified Input').first().json.Distribution[$runIndex].articles }}", "options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [604, -400], "id": "fbc68b57-1406-4da4-849d-5b92a8786265", "name": "Loop Over Items1"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [820, -340], "id": "bc293453-ec1e-410e-8c76-4fdf479efdfd", "name": "Aggregate"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1260, -540], "id": "1fe772d5-15c6-4d50-8b22-78cf8c2de9a0", "name": "Aggregate2"}, {"parameters": {"promptType": "define", "text": "=You are given a set of summarized paragraphs of a topic, generate a updated summary. Ensure that the summary is verbose and has enough infomation for another AI agent to understand and act upon.\n\n{{ $json.output }}\n\nHighlight any challenges or problems mentioned in the paragraphs.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1000, -340], "id": "f1b95fbe-c97f-4906-b05b-06040304c170", "name": "AI Summary - Interim", "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 5000}, {"parameters": {"promptType": "define", "text": "=You are given a set of summarized paragraphs of a topic, generate a updated summary. Ensure that this summary is comprehensive enough and verbose enough to be read and understood by another AI agent that will use this research summary as its background knowledge.\n\nAttempt to disregard infomation that is not relevent to the following industries: {{ $('Unified Input').item.json.Industries }}\n\n{{ $json.output }}\n\nHighlight any challenges or problems mentioned in the paragraphs. ", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1440, -540], "id": "a2a9dc7f-90fc-434b-8e68-2fa1c637111e", "name": "AI Summary - Final", "executeOnce": false, "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 5000}, {"parameters": {"promptType": "define", "text": "=You are given the markdown content from a web scraper, summarize it into a short and concise paragraph.\n\nBe sure to include the following information:\nTitle: {{ $json.Title }}\nDescription: {{ $json.Description }}\nKeywords: {{ $json.Keywords }}\nAuthor: {{ $json.Author }}\nSite Name: {{ $json.SiteName }}\n\nMarkdown:{{ $json.Markdown }}", "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [900, 240], "id": "7f5c95c5-94c9-4ab4-9dac-36d575329116", "name": "Webpage Summary", "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 5000}, {"parameters": {"promptType": "define", "text": "=You are given a set of summarized paragraphs of a topic, generate a updated summary\n\n{{ $json.text }}\n\nHighlight any challenges or problems mentioned in the paragraphs.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1100, 464], "id": "92360e69-b26d-406a-a3b1-f30c20678576", "name": "AI Summary - Website with deepcrawl", "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 5000}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": {{ $('Unified Input').first().json.progressEnd }},\n  \"message\": \"Generating finalized summary of research\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, -400], "id": "f6301f8f-89d0-43e2-82c7-327c1ed3e656", "name": "Progress API (Combined Summary)", "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": {{ $('Increment Progress').item.json.progress }},\n  \"message\": \"Summarizing {{ $('Wait for Count Item').item.json.title }}\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 20], "id": "c092a5d8-9e25-4559-926b-ad0311cbc188", "name": "Progress API (Scrape & Summarize)", "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"workflowId": {"__rl": true, "value": "exAoPK5idpMrJwoD", "mode": "list", "cachedResultName": "Crawl4AI"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"url": "={{ $json.url }}", "userQuery": "={{ $('Unified Input').item.json.Topic }}", "searchDepth": "={{ $('Unified Input').item.json.searchDepth }}", "maxPages": "={{ $('Unified Input').item.json.maxPages }}"}, "matchingColumns": [], "schema": [{"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "userQuery", "displayName": "userQuery", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "searchDepth", "displayName": "searchDepth", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "maxPages", "displayName": "maxPages", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [400, 460], "id": "ec36378a-5d65-43bb-8e58-f2d32a63b498", "name": "Execute Workflow"}, {"parameters": {"path": "scrape", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1280, 240], "id": "acc97b2c-f41a-4b50-b2e2-8160e65acdad", "name": "Webhook", "webhookId": "a7e553e6-e6dd-4a7d-a1f9-fee82eb6bade"}, {"parameters": {"assignments": {"assignments": [{"id": "7124f64a-07de-4879-bdbd-79c3e33e85aa", "name": "Topic", "value": "={{ $json.body.Topic }}", "type": "string"}, {"id": "eb39fe69-3987-4154-870f-a76fc52bde1b", "name": "Distribution", "value": "={{ $json.body.Distribution }}", "type": "array"}, {"id": "79cc8db9-34e6-4079-a583-bc6ac9e8c113", "name": "searchTerms", "value": "={{ $json.body.searchTerms }}", "type": "array"}, {"id": "7e91ec18-b794-4afd-a528-b57ca927f6a8", "name": "Industries", "value": "={{ $json.body.Industries }}", "type": "array"}, {"id": "814dca96-939f-493b-94e6-e6971db921a2", "name": "progressStart", "value": "={{ $json.body.progressStart }}", "type": "number"}, {"id": "f1da7eea-4eda-460b-beee-d4eef9db358d", "name": "progressEnd", "value": "={{ $json.body.progressEnd }}", "type": "number"}, {"id": "deab3b13-cf03-4065-8745-2cf3ee966f93", "name": "searchDepth", "value": "={{ $json.body.searchDepth }}", "type": "number"}, {"id": "22f41435-27ae-47ae-b3d9-5a78488d2ba0", "name": "maxPages", "value": "={{ $json.body.maxPages }}", "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1060, 240], "id": "e7697818-d9a9-4eee-a0ad-8bdd4d1c3f2a", "name": "Format Webhook"}, {"parameters": {"assignments": {"assignments": []}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1060, 60], "id": "2f3869b0-b80e-4dc6-93f2-c338ed8839ec", "name": "Format Subworkflow"}, {"parameters": {"assignments": {"assignments": [{"id": "7b439e28-f639-4dab-aa3f-0d5bbc69ccac", "name": "Topic", "value": "={{ $json.Topic }}", "type": "string"}, {"id": "91077fc1-2f0e-46aa-92bf-28d9acbff922", "name": "Distribution", "value": "={{ $json.Distribution }}", "type": "array"}, {"id": "1f33031f-1506-4e7c-a0fd-aa9605bcb099", "name": "searchTerms", "value": "={{ $json.searchTerms }}", "type": "array"}, {"id": "52af4356-0268-4508-ad62-55879b103a57", "name": "Industries", "value": "={{ $json.Industries }}", "type": "array"}, {"id": "6f94dcee-9294-49f9-8f6d-35dd3c7f24b0", "name": "progressStart", "value": "={{ $json.progressStart }}", "type": "number"}, {"id": "3d07fba8-e3e0-4ad1-8edf-b0337cf896d3", "name": "progressEnd", "value": "={{ $json.progressEnd }}", "type": "number"}, {"id": "6fc737fd-01f3-4740-9b0f-8c968e31ad6a", "name": "searchDepth", "value": "={{ $json.searchDepth }}", "type": "number"}, {"id": "843b576e-ecff-4661-b769-de4709056654", "name": "maxPages", "value": "={{ $json.maxPages }}", "type": "number"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-820, 60], "id": "2f79df10-f15a-41e0-b4bd-6410724f12c8", "name": "Unified Input"}, {"parameters": {"url": "http://localhost:5678/webhook/crawl4ai", "sendBody": true, "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $json.url }}"}, {"name": "userQuery", "value": "={{ $('Unified Input').item.json.Topic }}"}, {"name": "searchDepth", "value": "={{ $('Unified Input').item.json.searchDepth }}"}, {"name": "maxPages", "value": "={{ $('Unified Input').item.json.maxPages }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 240], "id": "5e7f0b44-bb0f-4192-93d9-103259fb1de3", "name": "Crawl API"}], "pinData": {"Webhook": [{"json": {"headers": {"accept": "application/json,text/html,application/xhtml+xml,application/xml,text/*;q=0.9, image/*;q=0.8, */*;q=0.7", "content-type": "application/json", "user-agent": "axios/1.8.3", "content-length": "1986", "accept-encoding": "gzip, compress, deflate, br", "host": "localhost:5678", "connection": "close"}, "params": {}, "query": {}, "body": {"Topic": "AI for Art", "Distribution": [{"term": "The New Stack", "articles": 1}, {"term": "Y Combinator", "articles": 1}, {"term": "News", "articles": 1}, {"term": "Impact", "articles": 1}, {"term": "Trends", "articles": 1}, {"term": "Challenges", "articles": 1}, {"term": "AI for art technology applications", "articles": 1}, {"term": "3D body generation using AI", "articles": 1}, {"term": "AI in entertainment industry art creation", "articles": 1}, {"term": "Healthcare and AI in 3D body modeling", "articles": 1}], "searchTerms": [{"url": "https://thenewstack.io/how-ai-can-help-you-learn-the-art-of-programming/", "title": "How AI Can Help You Learn the Art of Programming - The New Stack"}, {"url": "https://news.ycombinator.com/item?id=32486133", "title": "The AI Art Apocalypse | Hacker News"}, {"url": "https://www.nbcnews.com/tech/tech-news/viral-ai-made-art-trends-artists-concerns-rcna201448", "title": "Viral AI-made art trends are making artists even more worried about ..."}, {"url": "https://magazine.lmu.edu/articles/mimic-master/", "title": "AI's Impact on Artists – LMU Magazine"}, {"url": "https://www.nbcnews.com/tech/tech-news/viral-ai-made-art-trends-artists-concerns-rcna201448", "title": "Viral AI-made art trends are making artists even more worried about ..."}, {"url": "https://creator.nightcafe.studio/challenges", "title": "AI Art Challenges - NightCafe"}, {"url": "https://en.wikipedia.org/wiki/Artificial_intelligence_visual_art", "title": "Artificial intelligence visual art - Wikipedia"}, {"url": "https://www.meshy.ai/", "title": "Meshy AI - The #1 AI 3D Model Generator for Creators"}, {"url": "https://www.zhangjingna.com/blog/the-future-of-ai-art-and-automation-in-creative-industries", "title": "The Future of AI Art and Automation in Creative Industries - Jingna ..."}, {"url": "https://research.google/blog/advancing-medical-ai-with-med-gemini/", "title": "Advancing medical AI with Med-Gemini"}], "Industries": ["Technology", "Entertainment", "Healthcare"], "progressStart": "20", "progressEnd": "50", "searchDepth": 0, "maxPages": 5}, "webhookUrl": "http://************:5678/webhook/scrape", "executionMode": "production"}}]}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Format Subworkflow", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Summary - Interim", "type": "ai_languageModel", "index": 0}, {"node": "Webpage Summary", "type": "ai_languageModel", "index": 0}, {"node": "AI Summary - Website with deepcrawl", "type": "ai_languageModel", "index": 0}, {"node": "AI Summary - Final", "type": "ai_languageModel", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Wait for Count <PERSON>em", "type": "main", "index": 1}, {"node": "Count <PERSON>", "type": "main", "index": 0}]]}, "Count Items": {"main": [[{"node": "Wait for Count <PERSON>em", "type": "main", "index": 0}]]}, "Wait for Count Item": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Progress API (Combined Summary)", "type": "main", "index": 0}], [{"node": "Increment Progress", "type": "main", "index": 0}, {"node": "Crawl API", "type": "main", "index": 0}]]}, "Increment Progress": {"main": [[{"node": "Progress API (Scrape & Summarize)", "type": "main", "index": 0}]]}, "Wait for branches to complete": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Aggregate1": {"main": [[{"node": "AI Summary - Website with deepcrawl", "type": "main", "index": 0}]]}, "Loop Over Items1": {"main": [[{"node": "Aggregate2", "type": "main", "index": 0}], [{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "AI Summary - Interim", "type": "main", "index": 0}]]}, "Aggregate2": {"main": [[{"node": "AI Summary - Final", "type": "main", "index": 0}]]}, "AI Summary - Interim": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Webpage Summary": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}, "AI Summary - Website with deepcrawl": {"main": [[{"node": "Wait for branches to complete", "type": "main", "index": 1}]]}, "Progress API (Scrape & Summarize)": {"main": [[{"node": "Wait for branches to complete", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[]]}, "Webhook": {"main": [[{"node": "Format Webhook", "type": "main", "index": 0}]]}, "Format Webhook": {"main": [[{"node": "Unified Input", "type": "main", "index": 0}]]}, "Format Subworkflow": {"main": [[{"node": "Unified Input", "type": "main", "index": 0}]]}, "Unified Input": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "Progress API (Combined Summary)": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Crawl API": {"main": [[{"node": "Webpage Summary", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "bdfee043-27ef-4e1e-85e1-3c4ae8d1d131", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6a183a71e65f2e9a23d80da16e63124ad4da31dec0f6eafdc1e38fd4b099c77b"}, "id": "DpxWiFO1YTqmN40D", "tags": []}