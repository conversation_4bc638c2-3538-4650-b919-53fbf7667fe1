{"name": "Ingest Word", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "folder"}, {"name": "extension"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-300, 200], "id": "cbad2350-c9cf-491b-9b94-9be577d5d004", "name": "When Executed by Another Workflow"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// Function Node: Read DOCX File\nconst mammoth = require('mammoth');\nconst fs = require('fs').promises;\n\nasync function readDocx(filePath) {\n    const dataBuffer = await fs.readFile(filePath);\n    const result = await mammoth.extractRawText({buffer: dataBuffer});\n    return result.value;\n}\n\n// Example: Read DOCX from file path\nconst filePath = $('Unified Input').item.json.folder+\"/\"+$json.fileName;\nconst docxText = await readDocx(filePath);\n\nreturn {\n    json: {\n        name: $json.fileName,\n        text: docxText,\n    }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [580, 100], "id": "52813ce9-f4f7-428b-9147-5ca2a71c163f", "name": "Word Doc"}, {"parameters": {"fileSelector": "={{ $json.folder }}*.{{ $json.extension }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [360, 100], "id": "f215fc7c-eedf-4cc0-b7b8-ab85451a32a6", "name": "Read/Write Files from Disk"}, {"parameters": {"path": "doc", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-300, 0], "id": "697b006a-4d2a-4777-8557-4d03a665a401", "name": "Webhook", "webhookId": "2b655ea8-bac6-49d3-8da1-3eb6c5412f76"}, {"parameters": {"assignments": {"assignments": [{"id": "17aa1554-fa12-478a-9163-7139a7d0231c", "name": "folder", "value": "={{ $json.body.folder }}", "type": "string"}, {"id": "4f2ea287-786d-4de9-902a-69bf4310e3bc", "name": "extension", "value": "={{ $json.body.extension }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-80, 0], "id": "35954b9f-85e0-4ebf-a7da-f97c2a5ad4d1", "name": "Format Webhook"}, {"parameters": {"includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-80, 200], "id": "f57435f1-ce08-49a4-95f0-e96b02e5182d", "name": "Format Subworkflow"}, {"parameters": {"includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [140, 100], "id": "b0955f52-8b23-4032-9dc6-3a33c57ed0b1", "name": "Unified Input"}], "pinData": {"When Executed by Another Workflow": [{"json": {"folder": "/app/internal/Config/", "extension": "docx"}}], "Webhook": [{"json": {"headers": {"accept": "application/json,text/html,application/xhtml+xml,application/xml,text/*;q=0.9, image/*;q=0.8, */*;q=0.7", "content-type": "application/json", "user-agent": "axios/1.8.3", "content-length": "53", "accept-encoding": "gzip, compress, deflate, br", "host": "localhost:5678", "connection": "close"}, "params": {}, "query": {}, "body": {"folder": "/app/internal/Config/", "extension": "docx"}, "webhookUrl": "http://************:5678/webhook/doc", "executionMode": "production"}}]}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Format Subworkflow", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Word Doc", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Format Webhook", "type": "main", "index": 0}]]}, "Format Webhook": {"main": [[{"node": "Unified Input", "type": "main", "index": 0}]]}, "Format Subworkflow": {"main": [[{"node": "Unified Input", "type": "main", "index": 0}]]}, "Unified Input": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "66f52530-4ce2-4729-be06-0134002a1ac5", "meta": {"instanceId": "6a183a71e65f2e9a23d80da16e63124ad4da31dec0f6eafdc1e38fd4b099c77b"}, "id": "ig6FGfUuPmPad3tt", "tags": []}