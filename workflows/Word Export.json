{"name": "Word Export", "nodes": [{"parameters": {"mode": "runOnceForEachItem", "jsCode": "const docx = require('docx');\nconst { Document, Paragraph, TextRun, HeadingLevel, AlignmentType, Packer } = docx;\nconst fs = require('fs');\n\n// Helper function to write the document to a file\nasync function generateDocAsync(doc, filename) {\n  try {\n    const buffer = await Packer.toBuffer(doc);\n    fs.writeFileSync(filename, buffer);\n    console.log(\"success doc generate\");\n    return true;\n  } catch (error) {\n    console.log(\"error at write file: \" + error);\n    return false;\n  }\n}\n\nfunction textToWordDoc(text, filename = 'document', options = {}) {\n  // Default options\n  const defaultOptions = {\n    fontSize: 12,\n    fontFamily: 'Calibri',\n    lineSpacing: 1.15,\n    titleText: '',\n    titleSize: 16,\n    titleBold: true\n  };\n  \n  // Merge default options with provided options\n  const mergedOptions = { ...defaultOptions, ...options };\n    \n  // Array to hold all paragraphs\n  const paragraphDocs = [];\n  \n  // // Add title if provided from the options\n  // if (mergedOptions.titleText) {\n  //   paragraphDocs.push(\n  //     new Paragraph({\n  //       children: [\n  //         new TextRun({\n  //           text: mergedOptions.titleText,\n  //           bold: mergedOptions.titleBold,\n  //           size: mergedOptions.titleSize * 2, // Size in half-points\n  //           font: mergedOptions.fontFamily\n  //         })\n  //       ],\n  //       spacing: {\n  //         after: 200 // Space after the title\n  //       }\n  //     })\n  //   );\n  // }\n  \n  // Split the text into paragraphs\n  const paragraphs = text.split(/\\n+/);\n  \n  // Process each paragraph\n  paragraphs.forEach(paragraphText => {\n    if (paragraphText.trim() === '') return; // Skip empty paragraphs\n    \n    // Check for markdown headers\n    if (paragraphText.startsWith('###')) {\n      // Create a heading paragraph\n      paragraphDocs.push(\n        new Paragraph({\n          text: paragraphText.replace('###', '').trim(),\n          heading: HeadingLevel.HEADING_3,\n          spacing: {\n            before: 240,\n            after: 120\n          }\n        })\n      );\n      return;\n    }\n\n    // Check for line breaks \n    if (paragraphText.startsWith('---')) {\n      paragraphDocs.push(\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"\",\n              size: mergedOptions.fontSize * 2\n            })\n          ],\n          spacing: {\n            before: 480, // Extra space before\n            after: 480   // Extra space after\n          }\n        })\n      );\n      return;\n    }\n      \n    // Check for bullet points\n    if (paragraphText.startsWith('-')) {\n      // Create a bullet point paragraph\n      paragraphDocs.push(\n        new Paragraph({\n          text: paragraphText.substring(1).trim(),\n          bullet: {\n            level: 0\n          },\n          spacing: {\n            line: Math.round(mergedOptions.lineSpacing * 240)\n          }\n        })\n      );\n      return;\n    }\n\n    // Check for bold text within paragraphs (text between ** **)\n    if (paragraphText.includes('**')) {\n      const children = [];\n      let remainingText = paragraphText;\n      let boldStart = remainingText.indexOf('**');\n      \n      while (boldStart !== -1) {\n        // Add text before the bold section\n        if (boldStart > 0) {\n          children.push(\n            new TextRun({\n              text: remainingText.substring(0, boldStart),\n              size: mergedOptions.fontSize * 2,\n              font: mergedOptions.fontFamily\n            })\n          );\n        }\n        \n        // Find the end of the bold section\n        const boldEnd = remainingText.indexOf('**', boldStart + 2);\n        if (boldEnd === -1) break; // No closing ** found\n        \n        // Add the bold text\n        children.push(\n          new TextRun({\n            text: remainingText.substring(boldStart + 2, boldEnd),\n            bold: true,\n            size: mergedOptions.fontSize * 2,\n            font: mergedOptions.fontFamily\n          })\n        );\n        \n        // Update the remaining text\n        remainingText = remainingText.substring(boldEnd + 2);\n        boldStart = remainingText.indexOf('**');\n      }\n      \n      // Add any remaining text\n      if (remainingText) {\n        children.push(\n          new TextRun({\n            text: remainingText,\n            size: mergedOptions.fontSize * 2,\n            font: mergedOptions.fontFamily\n          })\n        );\n      }\n      \n      // Create a paragraph with the mixed text\n      paragraphDocs.push(\n        new Paragraph({\n          children: children,\n          spacing: {\n            line: Math.round(mergedOptions.lineSpacing * 240)\n          }\n        })\n      );\n    } else {\n      // Regular paragraph without special formatting\n      paragraphDocs.push(\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: paragraphText,\n              size: mergedOptions.fontSize * 2,\n              font: mergedOptions.fontFamily\n            })\n          ],\n          spacing: {\n            line: Math.round(mergedOptions.lineSpacing * 240)\n          }\n        })\n      );\n    }\n  });\n  \n  // Create a new document\n  const doc = new Document({\n    sections: [{\n      properties: {},\n      children: paragraphDocs\n    }]\n  });\n  \n  return generateDocAsync(doc, `${filename}.docx`);\n}\n\n// Example usage:\n// Extract title from the first line if it contains ** (bold)\nfunction processDocumentWithFormatting(text, filename) {\n  const lines = text.split('\\n');\n  let title = '';\n  let content = text;\n  \n  // Check if the first line is a title (bold)\n  if (lines[0].startsWith('**') && lines[0].endsWith('**')) {\n    title = lines[0].replace(/\\*\\*/g, '');\n    content = lines.slice(1).join('\\n');\n  }\n  \n  return textToWordDoc(content, filename, {\n    titleText: title,\n    titleSize: 18,\n    titleBold: true,\n    fontSize: 11,\n    fontFamily: 'Calibri',\n    lineSpacing: 1.15\n  });\n}\n\nconst filename = \"/app/internal/Output/\" + $json.title\nreturn processDocumentWithFormatting($json.text, filename)\n  .then(result => {\n    return {\n      json: {\n        completed: Boolean(result),\n        filename: filename,\n      }\n    };\n  })\n  .catch(error => {\n    console.log(error.message)\n    return {\n      json: {\n        completed: false,\n        filename: filename,\n        error: error.message\n      }\n    };\n  });"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 80], "id": "a1e0bb13-28f0-4880-a416-acaeebad0d03", "name": "Code"}, {"parameters": {"workflowInputs": {"values": [{"name": "text"}, {"name": "title"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [220, 180], "id": "1abe5b65-d94a-48fc-a432-352ef79b4509", "name": "When Executed by Another Workflow"}, {"parameters": {"path": "word-export", "responseMode": "lastNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, -20], "id": "65973c1c-337f-45d0-92af-9c2246b5ed1f", "name": "Webhook", "webhookId": "a1abd073-5ed9-4567-bb28-51212f7efdfa"}, {"parameters": {"assignments": {"assignments": [{"id": "4298b925-fbe1-44b1-9cae-b30139dc7ab7", "name": "title", "value": "={{ $json.body.title }}", "type": "string"}, {"id": "5fce8bf0-6ab2-4d4a-8c74-41f540bf0626", "name": "text", "value": "={{ $json.body.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, -20], "id": "65e233a8-d8e0-4e16-85a0-252be6f25e0e", "name": "Format Webhook"}], "pinData": {"When Executed by Another Workflow": [{"json": {"text": "**The Future of League of Legends Esports: Trends, Challenges, and Innovative Solutions**\n\nThe world of esports has witnessed an exponential rise in popularity over the past few years, none more so than in the realm of League of Legends (LoL). As we look towards the upcoming 2025 Mid-Season Invitational (MSI) set to take place in Vancouver, Canada, there's no denying that LoL esports is evolving and adapting with the landscape. In this blog post, we’ll delve into the current trends, highlight the challenges facing the ecosystem, and discuss how innovative solutions can enhance the experience for fans and players alike.\n\n### Current Trends in League of Legends Esports\n\nThe 2025 MSI promises to be an electrifying spectacle showcasing top-tier competition from five key regions: North America, Europe, China, Korea, and Southeast Asia. As diverse play styles clash on the virtual battleground, the introduction of novel tournament formats like the \"Full Fearless Draft\" aims to inject fresh strategic elements into the competition. Such innovation not only enhances gameplay but also broadens audience engagement.\n\nFan interaction continues to evolve as well, with Riot Games introducing interactive experiences through initiatives like Pick’Ems, which allows fans to predict match outcomes and win rewards. The inclusion of over 75 co-streamers from various backgrounds creates a multi-dimensional viewing experience, ensuring there’s something for every fan. With tailored merchandise—such as the Spirit Blossom Hwei skin—tying directly into the tournaments, esports enthusiasts can feel more connected to the action unfolding in front of them.\n\n### Challenges Facing the Ecosystem\n\nHowever, as promising as the future looks, the League of Legends esports community faces numerous challenges. From the complexity of tournament regulations and logistical issues that hinder accessibility, to the difficulty of maintaining fan engagement outside of major events, there are many areas that need attention.\n\n1. **Clarity and Communication**: Complex rules can lead to confusion among fans. Effective communication is essential to keeping everyone informed about eligibility and tournament formats.\n\n2. **Accessibility and Logistics**: The physical venues must be accessible to all attendees, considering diverse demographics. Additionally, the time zones can complicate viewing schedules for global fans.\n\n3. **Sustained Fan Engagement**: It's critical for Riot Games and its partners to innovate regularly, creating engaging content that attracts both casual gamers and avid fans alike.\n\n4. **Infrastructure Issues**: Concerns over server reliability and potential stream disruptions still plague the gaming experience, eroding the excitement during live events.\n\n5. **Diversity and Inclusion**: Ensuring broad representation for upcoming players and enhancing visibility for lower-tier teams are vital for fostering a robust community.\n\n### Implications for Companies and Organizations\n\nThese challenges directly impact brands, teams, and organizations involved in the esports ecosystem. Companies must recognize that clarity in marketing and communication plays a pivotal role in audience engagement. An inability to address these structural flaws may discourage new fans, limiting the games’ growth potential as a leading spectator sport.\n\nFurthermore, before investing resources into sponsorship or partnerships, companies must analyze how logistical barriers will be overcome. The esports industry thrives on live interactions; thus, any disruptions could result in the loss of invested capital.\n\n### How Solutions Can Alleviate Challenges\n\nAt Knovel Engineering, we are committed to helping companies leverage AI-augmented solutions to enhance operational efficiency. Among our offerings, **Solution A** stands out, providing companies with a comprehensive AI writing tool designed specifically for crafting extensive articles and content related to events like the MSI. By utilizing AI for article generation, organizations can immediately alleviate burdens related to content creation. \n\nWith **Solution A**, esports organizations can take advantage of AI-driven insights to facilitate clear communication about tournament rules, guidelines, and engagement opportunities. This will simplify and clarify information dissemination, ensuring all participants and fans remain informed about the latest updates.\n\n### Conclusion\n\nAs we approach the 2025 Mid-Season Invitational, the League of Legends esports landscape is undergoing dynamic transformations—significant opportunities coexisting alongside notable challenges. By embracing innovative solutions to enhance clarity and inclusivity, brands can better position themselves within this competitive environment.\n\nThe path forward relies on all stakeholders—from Riot Games and teams to corporations—collaboratively addressing these challenges head-on, ensuring a vibrant future for League of Legends esports.\n\n### Call to Action\n\nKnovel Engineering: Your Trusted AI Solutions Partner\n\nAt Knovel Engineering, we understand the complexities of reshaping the esports landscape. With our innovative AI solutions, including content generation and audience engagement tools, we empower esports organizations to enhance their outreach and communication strategies. If you're looking to excel in the rapidly evolving world of esports, partner with us to optimize your operations and drive success. Reach out to us today and discover how we can support your journey!", "title": "LOL Esports-2025-07-04T01:45:30.182-04:00"}}]}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Format Webhook", "type": "main", "index": 0}]]}, "Format Webhook": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "d31d9e68-d39d-482d-b5e9-980d35826f9a", "meta": {"instanceId": "6a183a71e65f2e9a23d80da16e63124ad4da31dec0f6eafdc1e38fd4b099c77b"}, "id": "yAeeKSXvez1O7WmY", "tags": []}