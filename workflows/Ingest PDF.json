{"name": "Ingest PDF", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "folder"}, {"name": "extension"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [180, 200], "id": "e49760df-fa07-481b-b570-a52348159cb2", "name": "When Executed by Another Workflow"}, {"parameters": {"fileSelector": "={{ $json.folder }}*.{{ $json.extension }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [400, 100], "id": "8a580d8a-b5a8-4561-8624-03e6ea965af0", "name": "Read/Write Files from Disk"}, {"parameters": {"operation": "pdf", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [620, 100], "id": "a8fbeee1-98f6-4bed-8bb4-7237fe9f1286", "name": "Extract from File"}, {"parameters": {"assignments": {"assignments": [{"id": "0fabfc75-bc97-4035-bfa9-eee950e88b23", "name": "fileName", "value": "={{ $('Read/Write Files from Disk').item.json.fileName }}", "type": "string"}, {"id": "76d33bbd-662c-47ef-81f4-cc59c4eae8c2", "name": "text", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [840, 100], "id": "2db10e27-921a-4021-a2e5-343be1f31cbc", "name": "<PERSON>"}, {"parameters": {"path": "pdf", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-40, 0], "id": "688ba6eb-9dc4-48f1-9b0a-ac66449ea9d0", "name": "Webhook", "webhookId": "2b655ea8-bac6-49d3-8da1-3eb6c5412f76"}, {"parameters": {"assignments": {"assignments": [{"id": "17aa1554-fa12-478a-9163-7139a7d0231c", "name": "folder", "value": "={{ $json.body.folder }}", "type": "string"}, {"id": "4f2ea287-786d-4de9-902a-69bf4310e3bc", "name": "extension", "value": "={{ $json.body.extension }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [180, 0], "id": "f8e0ce4a-0e7b-4285-b675-2dc5130f5a90", "name": "Format Webhook"}], "pinData": {}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Format Webhook", "type": "main", "index": 0}]]}, "Format Webhook": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "f0dbae0f-c8ba-479c-a9bb-8a774a9db0ac", "meta": {"instanceId": "6a183a71e65f2e9a23d80da16e63124ad4da31dec0f6eafdc1e38fd4b099c77b"}, "id": "SkwiPcyZfpo1TU90", "tags": []}