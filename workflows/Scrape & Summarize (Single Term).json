{"name": "Scrape & Summarize (Single Term)", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "title"}, {"name": "url"}, {"name": "Topic"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1080, 587], "id": "4023d9a4-d89c-48dd-b42b-3c0d0c3cd224", "name": "When Executed by Another Workflow"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [240, 432], "id": "a8516ea5-a0f3-410b-ad2a-9a3459d3d726", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "toEdGFaHKWeU2Izy", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "=You are given a set of summarized paragraphs of a topic, generate a updated summary\n\n{{ $json.text }}\n\nHighlight any challenges or problems mentioned in the paragraphs.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [160, 212], "id": "ddf270a9-93cc-4c05-80ba-f9ccc9891642", "name": "AI Summary - Markdown"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-420, 562], "id": "b2022282-9a05-48ea-8590-45abd3a3b808", "name": "Loop Over Items1"}, {"parameters": {"promptType": "define", "text": "=You are given the markdown content from a web scraper, summarize it into a short and concise paragraph.\n\nBe sure to include the following information:\nTitle: {{ $json.Title }}\nDescription: {{ $json.Description }}\nKeywords: {{ $json.Keywords }}\nAuthor: {{ $json.Author }}\nSite Name: {{ $json.SiteName }}\n\nMarkdown:{{ $json.Markdown }}", "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-200, 562], "id": "22468a9a-a67b-4c65-bf50-729a6f4d5c15", "name": "Basic LLM Chain"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "text"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-130, 312], "id": "c3d6a347-0ee4-4afb-97d5-cbc25bd15c67", "name": "Aggregate"}, {"parameters": {"amount": 2}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [230, 637], "id": "7d1834cc-f365-4c1b-8180-4e38369dca7d", "name": "Wait", "webhookId": "88bef627-e7f4-4c12-bb29-36af66766940"}, {"parameters": {"workflowId": {"__rl": true, "value": "exAoPK5idpMrJwoD", "mode": "list", "cachedResultName": "Crawl4AI"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"searchDepth": 1, "maxPages": 5, "url": "={{ $json.url }}", "userQuery": "={{ $('Unified Input').item.json.Topic }}"}, "matchingColumns": [], "schema": [{"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "userQuery", "displayName": "userQuery", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "searchDepth", "displayName": "searchDepth", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}, {"id": "maxPages", "displayName": "maxPages", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "number"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-1300, -48], "id": "e64630d3-4824-41e2-a4e8-77254914b725", "name": "Execute Workflow"}, {"parameters": {"includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-860, 562], "id": "7e54cf06-8d14-428d-9ad2-9958b21f05c7", "name": "Unified Input"}, {"parameters": {"path": "scrape-single", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1300, 387], "id": "cbde8dc7-769e-4d63-88d0-9bea0aa62c19", "name": "Webhook", "webhookId": "047195fe-3cbe-4d57-ae2f-a005e8f1358c"}, {"parameters": {"assignments": {"assignments": [{"id": "5a146826-7a21-4a82-a37a-d4fc10fbde2c", "name": "title", "value": "={{ $json.body.title }}", "type": "string"}, {"id": "afe5af1b-d7ee-41e8-b9c8-43ebfe17a253", "name": "url", "value": "={{ $json.body.url }}", "type": "string"}, {"id": "8fc27094-e6ae-49d5-8309-6663146795a2", "name": "Topic", "value": "={{ $json.body.Topic }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1080, 387], "id": "ba7f53f5-43a3-443e-a766-551cdfda1df3", "name": "Format Webhook"}, {"parameters": {"url": "http://localhost:5678/webhook/crawl4ai", "sendBody": true, "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $json.url }}"}, {"name": "userQuery", "value": "={{ $('Unified Input').item.json.Topic }}"}, {"name": "searchDepth", "value": "1"}, {"name": "maxPages", "value": "5"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-640, 562], "id": "19fefb4a-b79f-4eea-bb04-81bf48f0e3d8", "name": "Crawl API"}], "pinData": {"When Executed by Another Workflow": [{"json": {"title": "https://www.ibm.com/think/topics/ai-in-marketing", "url": "https://www.ibm.com/think/topics/ai-in-marketing", "Topic": "Marketing"}}, {"json": {"title": "https://www.marketermilk.com/blog/ai-marketing-tools", "url": "https://www.marketermilk.com/blog/ai-marketing-tools", "Topic": "Marketing"}}], "Webhook": [{"json": {"headers": {"accept": "application/json,text/html,application/xhtml+xml,application/xml,text/*;q=0.9, image/*;q=0.8, */*;q=0.7", "content-type": "application/json", "user-agent": "axios/1.8.3", "content-length": "240", "accept-encoding": "gzip, compress, deflate, br", "host": "localhost:5678", "connection": "close"}, "params": {}, "query": {}, "body": {"title": "https://developer.nvidia.com/blog/rapidly-generate-3d-assets-for-virtual-worlds-with-generative-ai/", "url": "https://developer.nvidia.com/blog/rapidly-generate-3d-assets-for-virtual-worlds-with-generative-ai/", "Topic": "AI for Art"}, "webhookUrl": "http://************:5678/webhook/scrape-single", "executionMode": "production"}}]}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Unified Input", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Summary - Markdown", "type": "ai_languageModel", "index": 0}, {"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Loop Over Items1": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}], [{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "AI Summary - Markdown": {"main": [[]]}, "Basic LLM Chain": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "AI Summary - Markdown", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[]]}, "Unified Input": {"main": [[{"node": "Crawl API", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Format Webhook", "type": "main", "index": 0}]]}, "Format Webhook": {"main": [[{"node": "Unified Input", "type": "main", "index": 0}]]}, "Crawl API": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "866ba851-0e41-43a1-9ca3-983b3e47f79a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6a183a71e65f2e9a23d80da16e63124ad4da31dec0f6eafdc1e38fd4b099c77b"}, "id": "VGhiJd0QQQDFYMgz", "tags": []}