{"name": "Crawl4AI", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "url"}, {"name": "userQuery"}, {"name": "searchDepth", "type": "number"}, {"name": "maxPages", "type": "number"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, 200], "id": "faa4a0eb-4bf1-4056-b629-79ddeae7010c", "name": "When Executed by Another Workflow"}, {"parameters": {"method": "POST", "url": "http://crawl4ai:11235/crawl", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"urls\": [\n    \"{{ $json.url }}\"\n  ],\n  \"crawler_config\": {\n    \"only_text\": true,\n    \"simulate_user\": true,\n    \"magic\": true,\n    \"wait_until\": \"domcontentloaded\",\n    \"page_timeout\": 120000,\n    \"excluded_tags\": [\n      \"script\", \"style\", \"noscript\", \"iframe\", \"nav\", \"footer\", \"header\",\n      \"form\", \"input\", \"button\", \"select\", \"option\", \"aside\", \"figure\",\n      \"figcaption\", \"canvas\", \"svg\", \"video\", \"audio\", \"object\", \"embed\",\n      \"picture\", \"meta\", \"link\", \"base\"\n    ],\n    \"scraping_strategy\": {\n      \"type\": \"WebScrapingStrategy\",\n      \"params\": {}\n    },\n    \"exclude_social_media_domains\": [\n      \"facebook.com\",\n      \"twitter.com\",\n      \"x.com\",\n      \"linkedin.com\",\n      \"instagram.com\",\n      \"pinterest.com\",\n      \"tiktok.com\",\n      \"snapchat.com\",\n      \"reddit.com\"\n    ],\n    \"deep_crawl_strategy\": {\n      \"type\": \"BFSDeepCrawlStrategy\",\n      \"params\": {\n        \"max_depth\": {{ $json.searchDepth }},\n        \"max_pages\": {{ $json.maxPages }}\n      }\n    },\n    \"markdown_generator\": {\n      \"type\": \"DefaultMarkdownGenerator\",\n      \"params\": {\n        \"content_filter\": {\n         \"type\": \"PruningContentFilter\",\n          \"params\": {\n            \"min_word_threshold\": 50,\n            \"threshold\": 0.5\n          }\n        },\n        \"options\": {\n          \"type\": \"dict\",\n          \"value\": {\n            \"ignore_links\": true,\n            \"ignore_images\": true\n          }\n        }\n      }\n    }\n  }\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 100], "id": "69727ad3-0634-44d9-8239-a083c8f1e0dc", "name": "HTTP Request"}, {"parameters": {"jsCode": "// Get all items\nconst items = $input.all();\n\n// Initialize an array to hold combined results\nlet combinedResults = [];\n\n// Loop through each item in items\nfor (const item of items) {\n  // Access the 'results' array in the current item\n  const results = item.json.results;\n\n  // Filter and map the results where success is true and status_code is 200\n  const filteredMapped = results\n    .filter(result => result.success === true && result.status_code === 200)\n    .map(result => ({\n      json: {\n        Title: result.metadata.title,\n        Description: result.metadata.description,\n        Keywords: result.metadata.keywords,\n        Author: result.metadata.author,\n        SiteName: result.metadata['og:site_name'],\n        Markdown: result.markdown.fit_markdown\n      }\n    }));\n\n  // Add the filtered and mapped results to the combinedResults array\n  combinedResults = combinedResults.concat(filteredMapped);\n}\n\n// Return the combined results\nreturn combinedResults;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 100], "id": "bca4541a-1440-42b9-87d5-9dbf071ffa2a", "name": "Split Out", "alwaysOutputData": true}, {"parameters": {"path": "crawl4ai", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-220, 0], "id": "f1827e3e-0862-4e78-a4eb-089b90c1222f", "name": "Webhook", "webhookId": "a7e553e6-e6dd-4a7d-a1f9-fee82eb6bade"}, {"parameters": {"assignments": {"assignments": [{"id": "7124f64a-07de-4879-bdbd-79c3e33e85aa", "name": "url", "value": "={{ $json.body.url }}", "type": "string"}, {"id": "eb39fe69-3987-4154-870f-a76fc52bde1b", "name": "userQuery", "value": "={{ $json.body.userQuery }}", "type": "string"}, {"id": "deab3b13-cf03-4065-8745-2cf3ee966f93", "name": "searchDepth", "value": "={{ $json.body.searchDepth }}", "type": "number"}, {"id": "22f41435-27ae-47ae-b3d9-5a78488d2ba0", "name": "maxPages", "value": "={{ $json.body.maxPages }}", "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [0, 0], "id": "749b56a4-fafd-4dfc-87d5-95d9f06d44b7", "name": "Format Webhook"}], "pinData": {}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Format Webhook", "type": "main", "index": 0}]]}, "Format Webhook": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "3956770a-24c0-4f68-87e0-cbe0a3625614", "meta": {"instanceId": "6a183a71e65f2e9a23d80da16e63124ad4da31dec0f6eafdc1e38fd4b099c77b"}, "id": "exAoPK5idpMrJwoD", "tags": []}