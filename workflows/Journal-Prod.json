{"name": "Journal-Prod", "nodes": [{"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1700, 1840], "id": "506bb790-ca9e-4eb3-8749-1fd94f921ce6", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "toEdGFaHKWeU2Izy", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "=Now, also generate some questions or terms I can use to search and research more about this topic: {{ $('Webhook Input').first().json['Chosen Topic'] }}\n\nI want this number of terms/questions: 4\nAlso note the following:\nRelevent industries: {{ $json.Industry }}\nPurpose of research: {{ $json.Purpose }}\n", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [-6160, 1020], "id": "e613ad33-25d5-4a26-a029-7da7206e831a", "name": "Search Term Generate", "retryOnFail": true}, {"parameters": {"promptType": "define", "text": "=You are representing Knovel Engineering, a local Singapore based startup that provides AI augemented solutions. Finally, write up a blog post, discussing the topic, and how a chosen solution is ideal. You will be provided the topic, a summary of the topic and a list of solutions to choose from. If there are no solutions that are ideal, then do not choose a solution but provide a generic statment on how we as a company can develop an AI augmented solution to fix this.\n\nThe format of the article is as follows:\n- Intro\n- The current trends\n- Challenges of the topic\n- Zoom into one key challenge that should be tackled\n- Implications of the challenge to companies\n- Showcase a solution + How this challenge is mitigated by the chosen solution\n- Conclusion\n- End with an call to action \"Knovel Engineering: Your Trusted AI Solutions Partner\" and a paragraph describing how our company and our solutions are able to support potential readers\n\nEnsure the the chosen solution is clearly linked to the stated challenges, describe how the solution can help to solve the challenges stated\n\nHere is the topic:\n{{ $('Webhook Input').first().json['Chosen Topic'] }}\n\nHere is the overaching purpose:\n{{ $('Webhook Input').first().json['Purpose'] }}\n\nHere are the industries this article will be relevent towards\n{{ $('Webhook Input').first().json.Industry }}\n\nHere are the solutions that Knovel provides:\n{{ $('Summarize solutions').first().json.text }}\n\nHere are the summarizations of the topic:\n{{ $('Scrape API').first().json.output }}\n\nYou are also provided an additional summary related to the topic, be sure to highlight the infomation from this summary in the final article (if text is 'NA', then disregard this section):\n{{ $('Additional Reference Output').first().json.output }}\n\nAlso, follow this rules when writing:\n{{ $('Rules Field').first().json.rules }}\n\nBe sure to include some SEO words into the article that can boost the score for it as well. Also make the article 800-1500 words long.\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [-1720, 1260], "id": "e6e8eb0a-2c41-45c6-bae7-67f447371ff9", "name": "Blog Writer", "retryOnFail": true}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "Writing", "contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-1580, 1840], "id": "4352be38-ad1b-49d3-890c-f3a87faa2fb7", "name": "Simple Memory"}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nvar combinedString = \"\";\nfor (const item of $input.first().json.solutions) {\n  combinedString += \"Name: \"\n  combinedString += item.name;\n  combinedString += \" | Text Description: \"\n  combinedString += item.text;\n  combinedString += \"\\n-------------------\\n\";\n}\n\nreturn [{\n  json: {\n    solutionSummary: combinedString\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3500, 720], "id": "5a699e9f-79a0-4094-a0ac-5722e3ccfebb", "name": "Translate Solutions to JSON"}, {"parameters": {"jsCode": "const allOutput = []\nfor (const item of $input.all()) {\n  // Create a JSON with these fields\n  // Name\n  // Text\n\n  const solution = {\n    name: item.json.fileName,\n    text: item.json.text,\n  }\n  allOutput.push(solution)\n}\n\n// Return a list of JSON\nreturn [{\n  json: {\n    solutions: allOutput\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3720, 720], "id": "9e85e418-feaf-49f3-ae4b-5d553c519e97", "name": "Parse Solution To JSON"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5bebd6e3-40a8-4a01-8025-4be851fb3aa8", "leftValue": "={{ $json.name }}", "rightValue": "writer_rules", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-3720, 1180], "id": "d9a6eff0-6ee4-4d8d-a5a8-67aef5a36d08", "name": "If", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "2969e0d5-4f81-4999-912b-fc17a6214220", "name": "rules", "value": "={{ $('DOC API').item.json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-3500, 1360], "id": "db75edd9-d0a3-421d-bfe7-9b039445f1b5", "name": "Rules Field"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2d458091-be70-4b4a-85fb-d38fc93b6bbc", "leftValue": "={{ $json.completed }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-600, 840], "id": "f59b1ae9-a1ff-4ec2-8ee5-2a53be7c95f1", "name": "If1"}, {"parameters": {"fileSelector": "={{ $json.filename }}.docx", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-380, 720], "id": "f5e6267d-60e6-4828-8bc7-4fe6dc9f5233", "name": "Read/Write Files from Disk"}, {"parameters": {"content": "## Ingest Solutions", "height": 456, "width": 1236, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3998, 424], "id": "86cdca2e-0e9a-446f-bc2e-7caf1598dcf8", "name": "Sticky Note2"}, {"parameters": {"content": "## Ingest writer's 'house' rules", "height": 416, "width": 776, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3960, 1080], "id": "8106e6e0-c945-4b0a-b8d2-2a19d17adf5f", "name": "Sticky Note3"}, {"parameters": {"content": "## Handle output\n- Write to directory\n- Return created word doc", "height": 532, "width": 972, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-840, 500], "id": "6d690ec8-e563-416d-ac97-5ebb19aba831", "name": "Sticky Note4"}, {"parameters": {"content": "## List of settings for sub-workflows\n\n### Read House Rules\nfolder: /app/internal/Config/\nextension: docx\n\n### Read Solutions\nfolder: /app/internal/Solutions/\nextension: docx\n\n### Output to Word Doc\ntext: {{ $json.output }}\ntitle: {{ $('Merge (Rules)').item.json['Chosen Topic'] }}-{{ $now }}", "height": 516, "width": 652, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2560, 360], "id": "05bc3e54-f7e9-4e77-b921-3d2e395b7f2e", "name": "Sticky Note1"}, {"parameters": {"promptType": "define", "text": "=You will proofread and fix any grammatical, format or structural errors in the provided article. Only output the article text, without any commentary or explanation or introductory responses. Output using markdown formatting without the text '```' or 'markdown'.\n\n{{ $('Blog Writer').item.json.output }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-1240, 1260], "id": "2abe3e87-44b6-4633-9314-5dd7054db887", "name": "Proofreader", "retryOnFail": true}, {"parameters": {"mode": "raw", "jsonOutput": "{\n  \"keywords\": [\n    \"The New Stack\",\n    \"Y Combinator\",\n    \"News\",\n    \"Impact\",\n    \"Trends\",\n    \"Challenges\"\n  ]\n}\n", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-6000, 840], "id": "2c25c8a1-330f-4807-821a-f7c64ff407ca", "name": "Search Keywords"}, {"parameters": {"jsonSchemaExample": "{\n  \"keywords\": [\"word1\", \"word2\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [-6000, 1240], "id": "e6e707f5-b453-438f-a3f6-3f15d23559de", "name": "Structured Output Parser"}, {"parameters": {"assignments": {"assignments": [{"id": "56aadae6-5017-4256-bfd8-0ccdbcb5ee98", "name": "keywords", "value": "={{ $json.output.keywords }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-5860, 1020], "id": "74a4717d-1ded-4e08-ab89-ddfd53cf9830", "name": "Format Output"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-5560, 900], "id": "a12a077f-fa20-4171-8298-1be149e3014a", "name": "Merge Search Terms"}, {"parameters": {"jsCode": "// Assuming input[0].json.list1 and input[1].json.list2\nconst list1 = $input.all()[0].json.keywords\nconst list2 = $input.all()[1].json.keywords\nreturn [\n  {\n    json: {\n      mergedList: [...list1, ...list2]\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-5380, 900], "id": "a3a88334-72e5-4fcb-95f5-3e5f3b3c0ef6", "name": "Merge Code"}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": 0,\n  \"message\": \"Start researching on {{ $('Webhook Input').first().json['Chosen Topic'] }}\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-4960, 900], "id": "1c938c5b-9652-4a5f-ac7b-e3d749c693fb", "name": "Progress API - Start", "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-4560, 900], "id": "a0e8e6cd-4c30-4bd0-a6a8-9cb2b673758f", "name": "Combine URL list"}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": 65,\n  \"message\": \"Understanding writer rules\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3500, 1160], "id": "aef33531-fccb-4d45-9af3-05a9fd393328", "name": "Progress API (Writer Rules)", "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": 70,\n  \"message\": \"Writing article\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1940, 1260], "id": "94407fa0-f2b5-4afb-8f27-3deaa130739a", "name": "Progress API (Writer)", "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": 85,\n  \"message\": \"Proofreading article\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1420, 1260], "id": "7dfa0837-ce12-4dc3-9424-3a575fc7afd8", "name": "Progress API (Proofreader)", "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": 95,\n  \"message\": \"Outputing article doc file to directory\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-380, 540], "id": "cc160d47-5849-431a-9e84-d352702aad9e", "name": "Progress API (Output document)", "onError": "continueRegularOutput"}, {"parameters": {"content": "## Write article", "height": 332, "width": 1216, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2180, 1140], "id": "39d24d34-d43d-4bf2-8c2b-f93e2bf6171e", "name": "<PERSON><PERSON>"}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": 100,\n  \"message\": \"Completed article!\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-20, 720], "id": "4819e423-1f60-4a4e-8371-0ee797552f5c", "name": "Progress API (Complete)", "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// // Get the input string containing URLs separated by newlines\n// const inputString = $('Webhook Input').first().json['Additional References']\n\n// // Split the string by newlines and filter out empty lines\n// const urls = inputString\n//   .split('\\n')\n//   .map(url => url.trim())\n//   .filter(url => url.length > 0);\n\n// // Return each URL as a separate item\n// return urls.map(url => ({\n//   json: {\n//     url: url\n//   }\n// }));\n\n\n// Replace 'Webhook Input' with the actual name of your node\n// Replace 'Additional References' with the actual field name containing the array\n\nconst array = $('Webhook Input').first().json['Additional References']\n\n// Optional: If you want to keep other fields from the original item, grab them here\n// For example: const otherField = $node[\"Webhook Input\"].json[\"otherField\"];\n\nif (!Array.isArray(array)) {\n  throw new Error(\"Expected an array in 'Additional References'\");\n}\n\nreturn array.map(url => ({\n  json: {\n    url, // Each item will have a 'url' field with the value from the array\n    // ... add other fields here if needed\n    // otherField\n  }\n}));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2780, 1160], "id": "6ac33c15-5f81-437e-b567-0cdf1b9212ad", "name": "Split Additional References"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "87e0afd4-d031-476f-8e62-e9b27ae1e9ce", "leftValue": "={{ $('Webhook Input').first().json['Additional References'] }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-3000, 1260], "id": "2d3d0963-35e9-47a0-aa1e-1fd0d4fc7407", "name": "If Additional References"}, {"parameters": {"content": "## Understand additional references (if any)", "height": 416, "width": 824}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3060, 1100], "id": "682eac6c-26ce-418f-bab4-6b318b714a82", "name": "Sticky Note5"}, {"parameters": {"promptType": "define", "text": "=You are provided text representing a list of solutions that a company offers, reformat, rearrange and summarize the text into a form that an AI agent can best understand\n\n {{ $json.solutionSummary }}", "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-3280, 720], "id": "95b59db0-0ce1-44ba-97ed-03e06d2af95e", "name": "Summarize solutions", "retryOnFail": true}, {"parameters": {"assignments": {"assignments": [{"id": "66c4c2bb-9585-42fe-91f2-71dd6071781a", "name": "output", "value": "NIL", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-2780, 1380], "id": "0f4524e6-3949-4f78-b7d6-c6e2594f0d4c", "name": "Dummy Output"}, {"parameters": {"assignments": {"assignments": [{"id": "66c4c2bb-9585-42fe-91f2-71dd6071781a", "name": "output", "value": "={{ $json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-2140, 1260], "id": "fb43882e-3ac2-4458-9bfd-7f6671f0b7c4", "name": "Additional Reference Output"}, {"parameters": {"httpMethod": "POST", "path": "journal", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-6540, 900], "id": "45020ff6-a5ec-4c73-845f-9f605748cc77", "name": "Webhook Trigger", "webhookId": "dff70487-da3c-4b06-97ac-72c53d3a7f8e"}, {"parameters": {"assignments": {"assignments": [{"id": "dea54432-4337-4c4c-9f18-ce1614bb645d", "name": "<PERSON><PERSON>", "value": "={{ $json.body.chosenTopic }}", "type": "string"}, {"id": "5884cac1-f77b-43e6-ba56-7f1ffadf20b8", "name": "Purpose", "value": "={{ $json.body.purpose }}", "type": "string"}, {"id": "e5e6c446-9aa4-4aae-adcf-f7347e313e06", "name": "Industry", "value": "={{ $json.body.industries }}", "type": "array"}, {"id": "59327abc-3049-4c6e-be7c-757176c28806", "name": "Number of Articles", "value": "={{ $json.body.numberOfArticles }}", "type": "number"}, {"id": "3f76cd87-1484-4150-b0d2-a2e7ab34fabe", "name": "Additional References", "value": "={{ $json.body.additionalReferences }}", "type": "array"}, {"id": "d0e013b7-cf69-43d2-ae5b-7ed4cde255bd", "name": "Search Depth", "value": "={{ $json.body.searchDepth }}", "type": "number"}, {"id": "5ff1e0e6-1feb-44de-b3ef-68e8aefbab68", "name": "<PERSON>", "value": "={{ $json.body.maxPages }}", "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-6360, 900], "id": "45482d9a-f9d0-4330-9be3-ed92efb9819d", "name": "Webhook Input"}, {"parameters": {"respondWith": "text", "responseBody": "Workflow failed to upload doc", "options": {"responseCode": 500}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [-200, 880], "id": "7e8a911f-561b-453e-9685-ff1c596b51b4", "name": "Failure Response"}, {"parameters": {"respondWith": "binary", "responseDataSource": "set", "options": {"responseHeaders": {"entries": [{"name": "fileName", "value": "={{ $json.fileName }}"}, {"name": "fileExtension", "value": "={{ $json.fileExtension }}"}, {"name": "Access-Control-Expose-Headers", "value": "filename, fileextension"}]}}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [-200, 720], "id": "d830611d-f0a7-4582-9128-6193f9dba7d6", "name": "Success Response"}, {"parameters": {"content": "## Decide on how many articles to search per term\n- Fixed to 10 terms (6 defined by us, 4 decided by the AI)\n- Input will take in the number of webpages to search into", "height": 300, "width": 560}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-5600, 780], "id": "0d961cbe-36b7-461d-b35a-2d423e9f4fb5", "name": "Sticky Note6"}, {"parameters": {"jsCode": "const numTerms = $input.first().json.mergedList.length;\nconst numArticles = $('Webhook Input').first().json['Number of Articles'];\n\nconst base = Math.floor(numArticles / numTerms);\nconst remainder = numArticles % numTerms;\n\n// Build the list with base distribution first\nlet mergedList = $input.first().json.mergedList;\nlet distributedSearchTerms = mergedList.map(term => ({\n  term,\n  articles: base\n}));\n\n// Randomly pick 'remainder' unique indexes and add 1 to each\nlet indexes = [];\nwhile (indexes.length < remainder) {\n  let idx = Math.floor(Math.random() * numTerms);\n  if (!indexes.includes(idx)) {\n    indexes.push(idx);\n    distributedSearchTerms[idx].articles += 1;\n  }\n}\n\nreturn {\n  distributedSearchTerms\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-5200, 900], "id": "7af04580-23ab-4cdf-8fd1-f21fae8f3e15", "name": "Calculate search distribution"}, {"parameters": {"url": "http://localhost:5678/webhook/websearch", "sendBody": true, "bodyParameters": {"parameters": [{"name": "Topic", "value": "={{ $('Webhook Input').first().json['Chosen Topic'] }}"}, {"name": "Search Term Information", "value": "={{ $('Calculate search distribution').item.json.distributedSearchTerms }}"}, {"name": "Industries", "value": "={{ $('Webhook Input').first().json.Industry }}"}, {"name": "progressStart", "value": "0"}, {"name": "progressEnd", "value": "20"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-4760, 900], "id": "ef0b281c-126d-4e90-9160-3ce2e2ed5d06", "name": "Websearch API"}, {"parameters": {"url": "http://localhost:5678/webhook/scrape", "sendBody": true, "bodyParameters": {"parameters": [{"name": "Topic", "value": "={{ $('Webhook Input').first().json['Chosen Topic'] }}"}, {"name": "Distribution", "value": "={{ $('Calculate search distribution').first().json.distributedSearchTerms }}"}, {"name": "searchTerms", "value": "={{ $json.data }}"}, {"name": "Industries", "value": "={{ $('Webhook Input').first().json.Industry }}"}, {"name": "progressStart", "value": "20"}, {"name": "progressEnd", "value": "50"}, {"name": "searchDepth", "value": "={{ $('Webhook Input').first().json['Search Depth'] }}"}, {"name": "maxPages", "value": "={{ $('Webhook Input').first().json['Max Pages'] }}"}]}, "options": {"timeout": 900000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-4360, 900], "id": "046341c4-995a-4182-af13-b6e5a1a6520c", "name": "Scrape API", "retryOnFail": true}, {"parameters": {"url": "http://localhost:5678/webhook/pdf", "sendBody": true, "bodyParameters": {"parameters": [{"name": "folder", "value": "/app/internal/Solutions/"}, {"name": "extension", "value": "pdf"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3940, 620], "id": "37a41e62-e1e2-4921-97cd-0d40b88d302b", "name": "PDF API"}, {"parameters": {"url": "http://localhost:5678/webhook/doc", "sendBody": true, "bodyParameters": {"parameters": [{"name": "folder", "value": "/app/internal/Config/"}, {"name": "extension", "value": "docx"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3880, 1180], "id": "99792f6a-a26f-4e3d-a5e5-743d79336344", "name": "DOC API"}, {"parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-2920, 620], "id": "9ea46934-1331-43d7-af62-6413f8ae3b3e", "name": "Wait for branch (Solutions)"}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": 60,\n  \"message\": \"Reading provided solutions\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3210, 520], "id": "f4d2da8a-0c4e-458c-a865-3bbc6c41e057", "name": "Progress API (Solutions)", "onError": "continueRegularOutput"}, {"parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-3320, 1260], "id": "112c0b68-5829-465b-b001-5c9db98cbfcb", "name": "Wait for branch (House Rules)"}, {"parameters": {"url": "http://localhost:5678/webhook/scrape-single", "sendBody": true, "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $json.url }}"}, {"name": "url", "value": "={{ $json.url }}"}, {"name": "Topic", "value": "={{ $('Webhook Input').first().json['Chosen Topic'] }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2500, 1160], "id": "7ff1874f-10ce-44ec-8660-4c5fe17ad90f", "name": "Single Scrape API"}, {"parameters": {"url": "http://localhost:5678/webhook/word-export", "sendBody": true, "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $('Webhook Input').first().json['Chosen Topic'] }}-{{ $now }}"}, {"name": "text", "value": "={{ $json.output }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-780, 840], "id": "449d1c00-a68b-4db3-b43d-2c0b97a54476", "name": "Output Word Doc API"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Search Term Generate", "type": "ai_languageModel", "index": 0}, {"node": "Blog Writer", "type": "ai_languageModel", "index": 0}, {"node": "Proofreader", "type": "ai_languageModel", "index": 0}, {"node": "Summarize solutions", "type": "ai_languageModel", "index": 0}]]}, "Search Term Generate": {"main": [[{"node": "Format Output", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Blog Writer", "type": "ai_memory", "index": 0}]]}, "Translate Solutions to JSON": {"main": [[{"node": "Summarize solutions", "type": "main", "index": 0}]]}, "Blog Writer": {"main": [[{"node": "Progress API (Proofreader)", "type": "main", "index": 0}]]}, "Parse Solution To JSON": {"main": [[{"node": "Translate Solutions to JSON", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Progress API (Writer Rules)", "type": "main", "index": 0}, {"node": "Rules Field", "type": "main", "index": 0}]]}, "Rules Field": {"main": [[{"node": "Wait for branch (House Rules)", "type": "main", "index": 1}]]}, "If1": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}, {"node": "Progress API (Output document)", "type": "main", "index": 0}], [{"node": "Failure Response", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Proofreader": {"main": [[{"node": "Output Word Doc API", "type": "main", "index": 0}]]}, "Search Keywords": {"main": [[{"node": "Merge Search Terms", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Search Term Generate", "type": "ai_outputParser", "index": 0}]]}, "Format Output": {"main": [[{"node": "Merge Search Terms", "type": "main", "index": 1}]]}, "Merge Search Terms": {"main": [[{"node": "Merge Code", "type": "main", "index": 0}]]}, "Merge Code": {"main": [[{"node": "Calculate search distribution", "type": "main", "index": 0}]]}, "Progress API - Start": {"main": [[{"node": "Websearch API", "type": "main", "index": 0}]]}, "Combine URL list": {"main": [[{"node": "Scrape API", "type": "main", "index": 0}]]}, "If Additional References": {"main": [[{"node": "Split Additional References", "type": "main", "index": 0}], [{"node": "Dummy Output", "type": "main", "index": 0}]]}, "Split Additional References": {"main": [[{"node": "Single Scrape API", "type": "main", "index": 0}]]}, "Progress API (Writer)": {"main": [[{"node": "Blog Writer", "type": "main", "index": 0}]]}, "Progress API (Proofreader)": {"main": [[{"node": "Proofreader", "type": "main", "index": 0}]]}, "Summarize solutions": {"main": [[{"node": "Wait for branch (Solutions)", "type": "main", "index": 1}]]}, "Progress API (Writer Rules)": {"main": [[{"node": "Wait for branch (House Rules)", "type": "main", "index": 0}]]}, "Dummy Output": {"main": [[{"node": "Additional Reference Output", "type": "main", "index": 0}]]}, "Additional Reference Output": {"main": [[{"node": "Progress API (Writer)", "type": "main", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Webhook Input", "type": "main", "index": 0}]]}, "Webhook Input": {"main": [[{"node": "Search Term Generate", "type": "main", "index": 0}, {"node": "Search Keywords", "type": "main", "index": 0}]]}, "Success Response": {"main": [[{"node": "Progress API (Complete)", "type": "main", "index": 0}]]}, "Calculate search distribution": {"main": [[{"node": "Progress API - Start", "type": "main", "index": 0}]]}, "Websearch API": {"main": [[{"node": "Combine URL list", "type": "main", "index": 0}]]}, "Scrape API": {"main": [[{"node": "PDF API", "type": "main", "index": 0}]]}, "PDF API": {"main": [[{"node": "Parse Solution To JSON", "type": "main", "index": 0}, {"node": "Progress API (Solutions)", "type": "main", "index": 0}]]}, "DOC API": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Wait for branch (Solutions)": {"main": [[{"node": "DOC API", "type": "main", "index": 0}]]}, "Progress API (Solutions)": {"main": [[{"node": "Wait for branch (Solutions)", "type": "main", "index": 0}]]}, "Wait for branch (House Rules)": {"main": [[{"node": "If Additional References", "type": "main", "index": 0}]]}, "Single Scrape API": {"main": [[{"node": "Additional Reference Output", "type": "main", "index": 0}]]}, "Output Word Doc API": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "ab5ccabd-1e35-4b65-82aa-ef0d9b390962", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6a183a71e65f2e9a23d80da16e63124ad4da31dec0f6eafdc1e38fd4b099c77b"}, "id": "bNm3Lz764G0CBlMn", "tags": [{"createdAt": "2025-07-16T00:55:26.667Z", "updatedAt": "2025-07-16T00:55:26.667Z", "id": "mD3VLUYjwLjWT97D", "name": "prod"}]}