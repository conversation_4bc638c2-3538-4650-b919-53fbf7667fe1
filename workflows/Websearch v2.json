{"name": "Websearch v2", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "Topic"}, {"name": "Search Term Information", "type": "array"}, {"name": "Industries", "type": "array"}, {"name": "progressStart", "type": "number"}, {"name": "progressEnd", "type": "number"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [240, -80], "id": "ceb0a2de-82a0-4d2d-835a-32d8869f7b14", "name": "When Executed by Another Workflow"}, {"parameters": {"fieldToSplitOut": "items", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1980, 20], "id": "b0ce91f9-9323-4332-a949-8777e5e5aeaf", "name": "Split Out Items"}, {"parameters": {"url": "https://customsearch.googleapis.com/customsearch/v1", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyDBosFnqpIcw7ubDM4d3KmP9w0WIEOtnrI"}, {"name": "q", "value": "={{ $('Loop Over Items').item.json.term }}, {{ $('Unified Input').item.json.Topic }} -site:facebook.com -site:twitter.com -site:x.com -site:linkedin.com -site:instagram.com -site:pinterest.com -site:tiktok.com -site:snapchat.com -site:reddit.com -site:youtube.com"}, {"name": "cx", "value": "75518db8d1b544921"}, {"name": "dateRestrict", "value": "y5"}, {"name": "num", "value": "={{ $('Loop Over Items').item.json.articles }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1740, 20], "id": "9d662a89-ec07-4df6-aceb-64556cf52228", "name": "Google Search"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "var extractData = {\n  url: $input.item.json.link,\n  title: $input.item.json.title,\n}\n\nreturn extractData;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2196, 16], "id": "c62eba38-0563-429b-8f4c-3870d31c5882", "name": "Code"}, {"parameters": {"method": "POST", "url": "http://frontend:80/api/progress", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"progress\": {{ $('Increment Progress').item.json.progress }},\n  \"message\": \"Searching about '{{ $('Loop Over Items').first().json.term }}'\",\n  \"status\": \"info\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2420, -176], "id": "7401a3ba-7408-4f85-a67d-fc51fc7e0620", "name": "Progress API", "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"fieldToSplitOut": "['Search Term Information']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [884, -192], "id": "3f3e8d8f-b262-4db7-b52f-2cb2a0e5be19", "name": "Split Out"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1556, -192], "id": "06e044fc-523c-453d-8189-a209b99d16ad", "name": "Loop Over Items"}, {"parameters": {"content": "## Websearch Workflow\n- Takes in a list of search terms\n- For each term, perform a search\n- Outputs a list of urls", "height": 144, "width": 272}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-32, -432], "id": "dc34e6ff-4465-4e39-93e4-24818cec2b49", "name": "<PERSON><PERSON>"}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2420, 16], "id": "42dedceb-a2b4-4890-92b3-49e72eede7f7", "name": "Wait", "webhookId": "a5930357-7636-42cd-b7e5-bf047dc0feab"}, {"parameters": {"jsCode": "// Start\nconst progressStart = $('Unified Input').first().json.progressStart\n// End\nconst progressEnd = $('Unified Input').first().json.progressEnd\n\n// Num items\nconst numItems = $('Count Items').first().json.numItems\n\n// Calculate 'step' based on progress start and end\nconst step = (progressEnd - progressStart) / numItems\nconst progressUpdated = $runIndex * step\n\nreturn {\n  \"success\": $input.first().json.success,\n  \"clientCount\": $input.first().json.clientCount,\n  \"progress\": Math.floor(progressUpdated)\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2196, -176], "id": "7dc8b4d0-50ac-4e9c-b7ab-876ce0811757", "name": "Increment Progress"}, {"parameters": {"jsCode": "const totalItems = $input.all().length;\n\nreturn {\n  \"numItems\": totalItems\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1100, -280], "id": "6f6e7516-7eb3-440a-a48a-c4ce5bdc4815", "name": "Count <PERSON>"}, {"parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [2644, 16], "id": "84dccab0-2e2a-4571-8af7-4830e8ef9f48", "name": "Wait for branches to complete"}, {"parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1332, -192], "id": "8b7ce0e8-f4f2-4085-89e9-d7136a9a63ad", "name": "Wait for Count <PERSON>em"}, {"parameters": {"path": "websearch", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 100], "id": "47d84f19-5ece-40c1-9cad-c81822db4b8e", "name": "Webhook", "webhookId": "a7e553e6-e6dd-4a7d-a1f9-fee82eb6bade"}, {"parameters": {"assignments": {"assignments": [{"id": "a7b728cb-133c-4ec3-9b08-f838b6468940", "name": "Topic", "value": "={{ $json.body.Topic }}", "type": "string"}, {"id": "101ad49f-a9ad-42be-911c-5e8324cb943c", "name": "Search Term Information", "value": "={{ $json.body['Search Term Information'] }}", "type": "array"}, {"id": "b14acf4c-8830-4181-9df1-9d5290af9b1c", "name": "Industries", "value": "={{ $json.body.Industries }}", "type": "array"}, {"id": "3448d836-e870-47af-a6a3-7725f9330584", "name": "progressStart", "value": "={{ $json.body.progressStart }}", "type": "string"}, {"id": "b5ff1424-76d7-409f-9956-fe3afd66692b", "name": "progressEnd", "value": "={{ $json.body.progressEnd }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 100], "id": "5488e6b6-2560-45ba-b0cb-3d4463cbee29", "name": "Format Webhook"}, {"parameters": {"assignments": {"assignments": [{"id": "43a88a9f-f700-4051-b820-c1d21cd80448", "name": "Topic", "value": "={{ $json.Topic }}", "type": "string"}, {"id": "550e0d89-1e61-44e1-8d4d-22ee6c558a60", "name": "Search Term Information", "value": "={{ $json['Search Term Information'] }}", "type": "array"}, {"id": "7123eb8a-793e-4ce5-8753-8ebd0058c0a8", "name": "Industries", "value": "={{ $json.Industries }}", "type": "array"}, {"id": "dbe12126-a07c-4430-a8f4-abe37ecb7b5b", "name": "progressStart", "value": "={{ $json.progressStart }}", "type": "string"}, {"id": "836fb7e7-5496-4b98-af04-826d94454302", "name": "progressEnd", "value": "={{ $json.progressEnd }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, -80], "id": "dad3ac81-b06b-466c-9e23-3d723daae373", "name": "Format Subworkflow"}, {"parameters": {"assignments": {"assignments": [{"id": "cbbb2242-9b35-4448-837a-b1fa426d97e6", "name": "Topic", "value": "={{ $json.Topic }}", "type": "string"}, {"id": "02b65d55-710d-4c6c-8a18-bc6a9b75fc41", "name": "Search Term Information", "value": "={{ $json['Search Term Information'] }}", "type": "array"}, {"id": "7dd4f3d6-24c9-4ca1-a115-7e37e361c3c9", "name": "Industries", "value": "={{ $json.Industries }}", "type": "array"}, {"id": "f15fa99c-6e76-4f23-b045-f01a611774f4", "name": "progressStart", "value": "={{ $json.progressStart }}", "type": "string"}, {"id": "c691fdb9-1d4e-41b2-a5f2-c02cf9fe4c59", "name": "progressEnd", "value": "={{ $json.progressEnd }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [680, -80], "id": "6ef32f7d-7556-4880-8166-920e830b6c6b", "name": "Unified Input"}], "pinData": {"When Executed by Another Workflow": [{"json": {"Topic": "213213", "Search Term Information": [{"term": "The New Stack", "articles": 1}, {"term": "Y Combinator", "articles": 2}, {"term": "News", "articles": 2}, {"term": "Impact", "articles": 2}, {"term": "Trends", "articles": 1}, {"term": "Challenges", "articles": 2}, {"term": "213213 technology impact", "articles": 2}, {"term": "213213 in tech industry", "articles": 1}, {"term": "213213 technology trends", "articles": 1}, {"term": "213213 technology applications", "articles": 1}], "Industries": ["Technology"], "progressStart": "0", "progressEnd": "20"}}], "Webhook": [{"json": {"headers": {"accept": "application/json,text/html,application/xhtml+xml,application/xml,text/*;q=0.9, image/*;q=0.8, */*;q=0.7", "content-type": "application/json", "user-agent": "axios/1.8.3", "content-length": "515", "accept-encoding": "gzip, compress, deflate, br", "host": "localhost:5678", "connection": "close"}, "params": {}, "query": {}, "body": {"Topic": "213213", "Search Term Information": [{"term": "The New Stack", "articles": 1}, {"term": "Y Combinator", "articles": 2}, {"term": "News", "articles": 2}, {"term": "Impact", "articles": 2}, {"term": "Trends", "articles": 1}, {"term": "Challenges", "articles": 2}, {"term": "213213 technology impact", "articles": 2}, {"term": "213213 in tech industry", "articles": 1}, {"term": "213213 technology trends", "articles": 1}, {"term": "213213 technology applications", "articles": 1}], "Industries": ["Technology"], "progressStart": "0", "progressEnd": "20"}, "webhookUrl": "http://************:5678/webhook/websearch", "executionMode": "production"}}]}, "connections": {"Split Out Items": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Format Subworkflow", "type": "main", "index": 0}]]}, "Google Search": {"main": [[{"node": "Split Out Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Google Search", "type": "main", "index": 0}, {"node": "Increment Progress", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Count <PERSON>", "type": "main", "index": 0}, {"node": "Wait for Count <PERSON>em", "type": "main", "index": 1}]]}, "Code": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Progress API": {"main": [[{"node": "Wait for branches to complete", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Wait for branches to complete", "type": "main", "index": 1}]]}, "Increment Progress": {"main": [[{"node": "Progress API", "type": "main", "index": 0}]]}, "Count Items": {"main": [[{"node": "Wait for Count <PERSON>em", "type": "main", "index": 0}]]}, "Wait for branches to complete": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait for Count Item": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Format Webhook", "type": "main", "index": 0}]]}, "Format Webhook": {"main": [[{"node": "Unified Input", "type": "main", "index": 0}]]}, "Format Subworkflow": {"main": [[{"node": "Unified Input", "type": "main", "index": 0}]]}, "Unified Input": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "c24e351b-d07b-4516-947a-28ef2b6b5c74", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6a183a71e65f2e9a23d80da16e63124ad4da31dec0f6eafdc1e38fd4b099c77b"}, "id": "QWJZZM3cbMZIzGpn", "tags": []}