# ===========================================
# SIMPLE DEVELOPMENT .gitignore
# ===========================================

# Environment files (for API keys, local settings)
*.env
*.env.local

# Generated blog content (your existing rules)
/internal/**/*.docx
/internal/**/*.pdf
/internal/Output/**/*

# Node.js (frontend dependencies)
node_modules/
npm-debug.log*

# Python cache (fixes your existing __pycache__ issue)
__pycache__/
*.pyc

# Database files
*.db
*.sqlite

# Development files
.DS_Store
Thumbs.db
*.log
*.tmp

# Editor files
.vscode/settings.json
.idea/

# Old workflows
/archived-workflows/*

# ===========================================
# EXTEND WHEN READY (uncomment as needed)
# ===========================================

# More environment files:
# *.env.development.local
# *.env.test.local

# More Node.js files:
# yarn-debug.log*
# yarn-error.log*
# coverage/

# More Python files:
*.py[cod]
build/
dist/

# More editor files:
# *.swp
# *.swo
# *.sublime-project

# Docker files:
# docker-compose.override.yml
# /filebrowser/filebrowser.db