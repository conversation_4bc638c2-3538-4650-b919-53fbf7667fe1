import requests

class APIGateway:
    def __init__(self, base_url, api_key):
        self.api_key = api_key
        self.base_url = base_url

    def post(self, endpoint, data):
        url = f"{self.base_url}/{endpoint}"
        headers = {"X-N8N-API-KEY": self.api_key}
        response = requests.post(url, headers=headers, json=data)

        if response.status_code != 200:
            raise Exception(f"Request failed with status code {response.status_code}")

        return response.json()

