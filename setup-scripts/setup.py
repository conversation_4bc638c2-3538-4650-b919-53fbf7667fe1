import sys
import json
import os
from pathlib import Path
from dotenv import load_dotenv

from api import APIGateway

# Load environment variables from .env file
load_dotenv()

N8N_API_KEY = os.getenv('N8N_API_KEY')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
API_ENDPOINT = "http://localhost:5678/api/v1"

if not N8N_API_KEY or not OPENAI_API_KEY:
    print("Error: N8N_API_KEY and OPENAI_API_KEY must be set in .env file")
    sys.exit(1)

# Handle credentials
# 1. Create openai credentials via API
# 2. Update each workflow to use the openai credentials

# Setup workflows
# 1. Reads the /workflows folder
# 2. Create workflow for each .json in /workflows
# 3. Activate each workflow via API

def activate_workflow(gateway, workflow_id):
    try:
        response = gateway.post(f"workflows/{workflow_id}/activate", {})
    except Exception as e:
        print(f"Error activating workflow: {e}")
        sys.exit(1)
    
    return response

def create_credentials(gateway, openai_api_key):
    data = {
        "name": "Autocreated OpenAI account",
        "type": "openAiApi",
        "data": {
            "apiKey": openai_api_key
        }
    }
    try:
        response = gateway.post("credentials", data)
    except Exception as e:
        print(f"Error creating credentials: {e}")
        sys.exit(1)

    return {
        "openAiApi": {
            "id": response["id"],
            "name": response["name"]
        }
    }

def insert_credentials_into_workflow(credentials, workflow_json):
    for node in workflow_json["nodes"]:
        if node["type"] == "@n8n/n8n-nodes-langchain.lmChatOpenAi":
            node["credentials"] = credentials
    return workflow_json

def create_workflow(gateway, workflow_json, credentials):
    credentialed_workflow = insert_credentials_into_workflow(credentials, workflow_json)

    # Create body 
    body = {
        "name": f"{credentialed_workflow['name']}-deployment",
        "nodes": credentialed_workflow["nodes"],
        "connections": credentialed_workflow["connections"],
        "settings": {
            "saveExecutionProgress": True,
            "saveManualExecutions": True,
            "saveDataErrorExecution": "all",
            "saveDataSuccessExecution": "all",
            "executionTimeout": 3600,
            "errorWorkflow": "VzqKEW0ShTXA5vPj",
            "timezone": "America/New_York",
            "executionOrder": "v1"
            },
        "staticData": {
            "lastId": 1
        }
    }
    try:
        response = gateway.post("workflows", body)
    except Exception as e:
        print(f"Error creating workflow: {e}")
        sys.exit(1)

    workflow_id = response["id"]
    activate_workflow(gateway, workflow_id)

def main():
    gateway = APIGateway(API_ENDPOINT, N8N_API_KEY)
    credentials = create_credentials(gateway, OPENAI_API_KEY)

    # Open /workflows folder
    # For each .json file, create workflow
    # Insert credentials into workflow
    # Activate workflow
    for file_path in Path("workflows/").iterdir():
        if file_path.suffix == '.json':
            with open(file_path, 'r') as file:
                workflow_json = json.load(file)
                create_workflow(gateway, workflow_json, credentials)

if __name__ == "__main__":
    main()
