# Automated Blog Writing

## Technical Setup
1. Run `docker compose build`
2. Set `HOST_IP` in `.env`
3. Run `docker compose up -d`
4. Go to `http://<HOST_IP>:5678`
5. Sign up with a shared email
6. Activate free license key from email
7. Create a workflow for each JSON file in /workflows folder (Click `...` icon and import from file)
8. For workflows that use sub-workflows, re-link the workflow (The linkage is not preserved when fresh installing).
Follow the sticky note with instructions on what specific values to set for each node
9. Set 'Automated Journal' to active and publish the production URL from 'On form submission'

### OpenAI
Set up credential account with OpenAI API key and set the model in "Automated Journal" and "Scrap and Summarize" workflow to use the account.

### Filebrowser
Create a directory `filebroswer` at root level if it does not exist. Then run 
```
touch filebrowser/filebrowser.db
touch filebrowser/settings.json
```
Write these values into settings.json
```
{
    "port": 80,
    "baseURL": "",
    "address": "",
    "log": "stdout",
    "database": "/database/filebrowser.db",
    "root": "/srv"
}
```
Login infomation is created as 
`username`: `admin`
`password`: `admin`
 
## User Guide
### Create journal
1. Go to `http://<HOST_IP>:5678`
2. Login into shared account
3. Open up "Automated Journal"
4. Press 'test workflow' and input topic and number of searches

### Adding Solution Documents
1. Go to `http://<HOST_IP>:8095`
2. Login with username: "admin" and password: "admin"
3. Navigate to `Solutions` folder and add documents (Ensure that the file is .docx)

### Setting House Rules
1. Go to `http://<HOST_IP>:8095`
2. Login with username: "admin" and password: "admin"
3. Navigate to `Config` folder and overwrite existing doc (Ensure that the file is .docx)
4. Name the file "writer_rules"

### Viewing output
1. Go to `http://<HOST_IP>:8095`
2. Login with username: "admin" and password: "admin"
3. Navigate to `Output` folder and view/download documents

## Improvements to be made
### 1. Able to cite sources
Lists the link which had the original data at the bottom of article and where it is cited in-text
### 2. Select a 'topic' and 'domain' (e.g. Generative AI in Public Safety)
Do research on topic, domain and combination of both. Able to gather specific challenges faced by a domain and link to specific benefits of a topic.
