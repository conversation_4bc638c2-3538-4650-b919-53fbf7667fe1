import { useState, useEffect, useRef } from 'react';

function useWebSocket(url) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState(null);
  const wsRef = useRef(null);
  const reconnectIntervalRef = useRef(null);

  useEffect(() => {
    if (!url) return;

    const connectWebSocket = () => {
      console.log(`Attempting to connect to WebSocket: ${url}`);
      try {
        wsRef.current = new WebSocket(url);

        wsRef.current.onopen = () => {
          console.log(`✅ WebSocket connected successfully to ${url}`);
          setIsConnected(true);
          if (reconnectIntervalRef.current) {
            clearInterval(reconnectIntervalRef.current);
            reconnectIntervalRef.current = null;
          }
        };

        wsRef.current.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            // Validate message format
            if (typeof data === 'object' && data !== null) {
              const { progress, message, status } = data;

              // Ensure progress is a valid number between 0 and 100
              const validProgress = typeof progress === 'number' &&
                                  progress >= 0 &&
                                  progress <= 100 ? progress : null;

              // Ensure message is a string
              const validMessage = typeof message === 'string' ? message : '';

              // Ensure status is a valid status type
              const validStatus = ['info', 'success', 'warning', 'error'].includes(status)
                                ? status : 'info';

              // Create validated message object
              const validatedMessage = {
                progress: validProgress,
                message: validMessage,
                status: validStatus,
                timestamp: new Date().toISOString()
              };

              console.log('📨 WebSocket message received:', validatedMessage);
              setLastMessage(validatedMessage);
            } else {
              console.warn('⚠️ Invalid WebSocket message format:', data);
            }
          } catch (error) {
            console.error('❌ Error parsing WebSocket message:', error);
          }
        };

        wsRef.current.onclose = (event) => {
          console.log(`❌ WebSocket disconnected. Code: ${event.code}, Reason: ${event.reason}`);
          setIsConnected(false);

          // Attempt to reconnect
          if (!reconnectIntervalRef.current) {
            reconnectIntervalRef.current = setInterval(() => {
              console.log('🔄 Attempting to reconnect WebSocket...');
              connectWebSocket();
            }, 2000);
          }
        };

        wsRef.current.onerror = (error) => {
          console.error('❌ WebSocket error:', error);
          setIsConnected(false);
        };
      } catch (error) {
        console.error('❌ Failed to create WebSocket connection:', error);
        setIsConnected(false);
      }
    };
    
    connectWebSocket();
    
    // Cleanup function
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectIntervalRef.current) {
        clearInterval(reconnectIntervalRef.current);
      }
    };
  }, [url]);

  // Function to send messages through the WebSocket
  const sendMessage = (data) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(data));
      return true;
    }
    return false;
  };

  return { isConnected, lastMessage, sendMessage };
}

export default useWebSocket;