import React, { useState, useEffect, useRef } from 'react';
import './App.css';
import WorkflowForm from './components/WorkflowForm';
import ProgressMonitor from './components/ProgressMonitor';
import LoadingSpinner from './components/LoadingSpinner';

function App() {
  const [config, setConfig] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const progressMonitorRef = useRef();
  
  useEffect(() => {
    // Load configuration from server
    fetch('/api/config')
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        setConfig(data);
        setIsLoading(false);
      })
      .catch(error => {
        console.error('Error loading configuration:', error);
        setError('Failed to load application configuration. Please refresh the page.');
        
        // Fallback configuration
        setConfig({
          n8nWebhookUrl: 'http://localhost:5678/webhook/journal',
          progressWsUrl: 'ws://localhost:8081/progress'
        });
        setIsLoading(false);
      });
  }, []);

  if (isLoading) {
    return (
      <div className="loading">
        <LoadingSpinner size="large" text="Loading application..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }

  return (
    <div className="container">
      <h1>Automated Marketing Journal</h1>
      <WorkflowForm
        webhookUrl={config.n8nWebhookUrl}
        progressMonitorRef={progressMonitorRef}
      />
      <ProgressMonitor ref={progressMonitorRef} />
    </div>
  );
}

export default App;
