/* Import your existing styles */
.container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  transition: transform 0.3s ease;
}

.container h1 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 30px;
  color: #333;
  font-size: 2rem;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.workflow-form-container {
  margin-bottom: 30px;
}

.workflow-form-container h2 {
  margin-top: 0;
  color: #333;
  font-size: 1.5rem;
}

.form-actions {
  display: flex;
  gap: 10px;
}

.reset-btn {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  flex: 1;
}

.reset-btn:hover {
  background-color: #e9ecef;
}

.reset-btn:disabled {
  background: #f8f9fa;
  color: #aaa;
  cursor: not-allowed;
}

.submit-btn {
  flex: 2;
}

.success-message {
  background-color: #e8f5e9;
  color: #1b5e20;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  border-left: 4px solid #4caf50;
}
/* Add more styles from your existing CSS */
