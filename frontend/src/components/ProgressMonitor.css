.progress-section {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.progress-section h2 {
  margin-top: 0;
  color: #333;
  font-size: 1.5rem;
}

.connection-status {
  margin-bottom: 15px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
}

.connection-status.connected {
  background-color: #e6f7e6;
  color: #2c7a2c;
}

.connection-status.disconnected {
  background-color: #fbe9e7;
  color: #c62828;
}

.last-update {
  font-size: 12px;
  color: #666;
  text-align: right;
  margin-bottom: 15px;
  font-style: italic;
}

.progress-container {
  margin-bottom: 20px;
}

.progress-bar {
  height: 24px;
  background-color: #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 12px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  transition: width 0.8s ease;
  position: relative;
  border-radius: 12px;
}

.progress-info {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.progress-success {
  background: linear-gradient(90deg, #4caf50 0%, #2e7d32 100%);
}

.progress-warning {
  background: linear-gradient(90deg, #ff9800 0%, #f57c00 100%);
}

.progress-error {
  background: linear-gradient(90deg, #f44336 0%, #c62828 100%);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #555;
  margin-bottom: 15px;
}

.progress-percentage {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.progress-message {
  font-style: italic;
  color: #666;
}

.status-message {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  border-left: 4px solid;
  animation: fadeIn 0.3s ease-in;
}

.status-indicator {
  font-size: 18px;
  margin-right: 12px;
  flex-shrink: 0;
}

.status-text {
  flex: 1;
  font-weight: 500;
}

.status-info {
  background-color: #e3f2fd;
  color: #0d47a1;
  border-left-color: #2196f3;
}

.status-success {
  background-color: #e8f5e9;
  color: #1b5e20;
  border-left-color: #4caf50;
}

.status-warning {
  background-color: #fff8e1;
  color: #ff6f00;
  border-left-color: #ff9800;
}

.status-error {
  background-color: #ffebee;
  color: #b71c1c;
  border-left-color: #f44336;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.instructions {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
}

.instructions h3 {
  margin-top: 0;
  font-size: 16px;
  color: #333;
}

.instructions p {
  margin: 8px 0;
  font-size: 14px;
  color: #555;
}