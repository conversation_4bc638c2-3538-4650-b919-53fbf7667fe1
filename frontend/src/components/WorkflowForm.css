.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  flex: 2;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  color: #e53e3e;
  margin-top: 10px;
  padding: 10px;
  background-color: #fff5f5;
  border-radius: 8px;
  border-left: 4px solid #e53e3e;
}

.success-message {
  color: #38a169;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f0fff4;
  border-radius: 8px;
  border-left: 4px solid #38a169;
}

.download-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.success-text {
  font-weight: 600;
  font-size: 16px;
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: rgba(56, 161, 105, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(56, 161, 105, 0.2);
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-weight: 600;
  font-size: 14px;
  color: #2d3748;
}

.file-size {
  font-size: 12px;
  color: #718096;
  font-style: italic;
}

.download-btn {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.download-btn:hover {
  background: linear-gradient(135deg, #2f855a 0%, #276749 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(56, 161, 105, 0.3);
}

.download-btn:active {
  transform: translateY(0);
}

.field-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  margin-top: -4px;
}

.industry-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.industry-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.industry-item:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.2);
}

.industry-item input[type="checkbox"] {
  width: auto;
  margin-right: 10px;
  margin-bottom: 0;
  transform: scale(1.1);
  cursor: pointer;
}

.checkbox-label {
  margin: 0 !important;
  font-weight: 500;
  cursor: pointer;
  user-select: none;
}

.selected-industries {
  margin-top: 12px;
  padding: 10px;
  background-color: #f7fafc;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
}

.custom-industry-input {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  animation: slideDown 0.3s ease-out;
}

.custom-industry-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.custom-industry-field {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #cbd5e0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
}

.custom-industry-field:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.custom-industry-field::placeholder {
  color: #a0aec0;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  margin-bottom: 24px;
  border-bottom: 2px solid #e2e8f0;
}

.tab-button {
  background: none;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  color: #718096;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.tab-button:hover {
  color: #4a5568;
  background-color: rgba(102, 126, 234, 0.05);
}

.tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background-color: rgba(102, 126, 234, 0.1);
}

.tab-content {
  animation: fadeIn 0.3s ease-in;
}

/* Sub-tab navigation styles */
.sub-tab-navigation {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.sub-tab-button {
  background: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sub-tab-button:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.sub-tab-button.active {
  background: #4299e1;
  color: white;
  border-color: #4299e1;
}

.sub-tab-content {
  padding-top: 8px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slider Styles */
.slider-container {
  margin-top: 8px;
}

.slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e2e8f0;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #718096;
}

.references-textarea {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
}

.url-count {
  margin-top: 8px;
  font-size: 12px;
  color: #718096;
  font-style: italic;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.reset-btn {
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  flex: 1;
}

.reset-btn:hover {
  background: #cbd5e0;
}

.reset-btn:disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .industry-container {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .submit-btn,
  .reset-btn {
    flex: none;
    width: 100%;
  }

  .custom-industry-input {
    margin-top: 12px;
    padding: 12px;
  }

  .custom-industry-field {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .tab-navigation {
    flex-direction: column;
    gap: 0;
  }

  .tab-button {
    padding: 16px;
    text-align: center;
    border-bottom: 1px solid #e2e8f0;
  }

  .tab-button.active {
    border-bottom-color: #667eea;
  }

  .references-textarea {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .file-info {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .download-btn {
    width: 100%;
    justify-content: center;
  }

  .sub-tab-navigation {
    flex-direction: column;
    gap: 8px;
  }

  .sub-tab-button {
    width: 100%;
    text-align: center;
  }
}