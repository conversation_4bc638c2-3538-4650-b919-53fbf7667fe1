import React, { useState } from 'react';
import './WorkflowForm.css';

function WorkflowForm({ webhookUrl, progressMonitorRef }) {
  const initialFormState = {
    chosenTopic: '',
    purpose: '',
    industries: [], // Changed from single industry to array of industries
    customIndustry: '', // For "Other" industry text input
    // Advanced Options
    numberOfArticles: 15, // Default to middle value (10-30 range)
    additionalReferences: '',

    // Crawling Settings
    searchDepth: 1, // Default search depth
    maxPages: 5,   // Default max pages to crawl

    // targetAudience: '',
    // keyPoints: '',
    // tone: 'informative'
  };

  // Available industry options
  const industryOptions = [
    'Technology',
    'Financial Services',
    'Healthcare',
    'Manufacturing',
    'Retail',
    'Education',
    'Real Estate',
    'Automotive',
    'Energy',
    'Entertainment',
    'Food & Beverage',
    'Transportation',
    'E-commerce',
    'Logistics & Supply Chain',
    'Public Sector',
    'Security',
    'Other',
  ];

  const [formData, setFormData] = useState(initialFormState);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState('basic'); // 'basic' or 'advanced'
  const [activeAdvancedTab, setActiveAdvancedTab] = useState('general'); // 'general' or 'crawling'
  const [downloadFile, setDownloadFile] = useState(null); // Store file data for download

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [id]: value
    }));

    // Clear success message when form is edited after successful submission
    if (success) setSuccess(false);
  };

  const handleCustomIndustryChange = (e) => {
    const { value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      customIndustry: value
    }));

    // Clear success message when form is edited after successful submission
    if (success) setSuccess(false);
  };

  const handleSliderChange = (e) => {
    const { id, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [id]: parseInt(value, 10)
    }));

    // Clear success message when form is edited after successful submission
    if (success) setSuccess(false);
  };

  const handleIndustryChange = (industry) => {
    setFormData(prevData => {
      const currentIndustries = prevData.industries;
      const isSelected = currentIndustries.includes(industry);

      let updatedIndustries;
      let updatedCustomIndustry = prevData.customIndustry;

      if (industry === 'Other') {
        // Handle "Other" option specially
        if (isSelected) {
          // Deselecting "Other" - remove it and clear custom text
          updatedIndustries = currentIndustries.filter(item => item !== industry);
          updatedCustomIndustry = '';
        } else {
          // Selecting "Other" - add it to the list
          updatedIndustries = [...currentIndustries, industry];
        }
      } else {
        // Handle regular industry options
        updatedIndustries = isSelected
          ? currentIndustries.filter(item => item !== industry)
          : [...currentIndustries, industry];
      }

      return {
        ...prevData,
        industries: updatedIndustries,
        customIndustry: updatedCustomIndustry
      };
    });

    // Clear success message when form is edited after successful submission
    if (success) setSuccess(false);
  };

  const resetForm = () => {
    setFormData(initialFormState);
    setError(null);
    setSuccess(false);
    setDownloadFile(null);

    // Reset progress monitor if ref is available
    if (progressMonitorRef && progressMonitorRef.current) {
      progressMonitorRef.current.reset();
    }
  };

  const handleDownload = () => {
    if (!downloadFile) {
      setError('No file available for download.');
      return;
    }

    try {
      // Create download link
      const url = window.URL.createObjectURL(downloadFile.blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = downloadFile.fileName;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('File download initiated:', downloadFile.fileName);
    } catch (error) {
      console.error('Error downloading file:', error);
      setError('Failed to download file. Please try again.');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);
    setDownloadFile(null);

    // Reset progress monitor when starting new workflow
    if (progressMonitorRef && progressMonitorRef.current) {
      progressMonitorRef.current.reset();
    }

    // Validate that at least one industry is selected
    if (formData.industries.length === 0) {
      setError('Please select at least one industry.');
      setIsSubmitting(false);
      return;
    }

    // Validate that if "Other" is selected, custom industry text is provided
    if (formData.industries.includes('Other') && !formData.customIndustry.trim()) {
      setError('Please specify the custom industry when "Other" is selected.');
      setIsSubmitting(false);
      return;
    }

    // Validate additional references URLs if provided
    if (formData.additionalReferences.trim()) {
      const urls = formData.additionalReferences.split('\n').filter(url => url.trim());
      const urlPattern = /^https?:\/\/.+/i;
      const invalidUrls = urls.filter(url => !urlPattern.test(url.trim()));

      if (invalidUrls.length > 0) {
        setError('Please ensure all additional references are valid URLs starting with http:// or https://');
        setIsSubmitting(false);
        return;
      }
    }

    try {
      // Prepare the data for submission
      const submissionData = {
        ...formData,
        // Replace "Other" with the custom industry text in the industries array
        industries: formData.industries.map(industry =>
          industry === 'Other' ? formData.customIndustry.trim() : industry
        ),
        // Process additional references - convert to array of URLs
        additionalReferences: formData.additionalReferences.trim()
          ? formData.additionalReferences.split('\n')
              .map(url => url.trim())
              .filter(url => url.length > 0)
          : []
      };

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submissionData)
      });

      if (!response.ok) {
        // Handle different error status codes
        let errorMessage = 'Failed to start workflow. Please try again.';

        try {
          // Try to parse error response as JSON to get server error message
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          } else if (typeof errorData === 'string') {
            errorMessage = errorData;
          }
        } catch (parseError) {
          // If response is not JSON, use status-specific messages
          if (response.status === 500) {
            errorMessage = 'Server error occurred. Please try again later.';
          } else if (response.status === 400) {
            errorMessage = 'Invalid request data. Please check your inputs.';
          } else if (response.status === 404) {
            errorMessage = 'Webhook endpoint not found. Please check configuration.';
          } else if (response.status >= 500) {
            errorMessage = 'Server error occurred. Please try again later.';
          } else if (response.status >= 400) {
            errorMessage = 'Request failed. Please check your inputs and try again.';
          }
        }

        throw new Error(errorMessage);
      }

      console.log("Response received:", response);
      console.log("Response status:", response.status);
      console.log("Response headers:", [...response.headers.entries()]);

      // Check if response contains a file (docx)
      const contentType = response.headers.get('content-type');
      console.log("Content-Type:", contentType);

      if (contentType && (
        contentType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ||
        contentType.includes('application/octet-stream') ||
        contentType.includes('application/msword') ||
        contentType.includes('application/vnd.ms-word')
      )) {
        // Handle file download response
        const blob = await response.blob();

        // Get custom headers from n8n
        const fileName = response.headers.get('fileName') || 'document.docx';
        const fileExtension = response.headers.get('fileExtension') || 'docx';

        // Ensure filename has correct extension
        const finalFileName = fileName.endsWith(`.${fileExtension}`)
          ? fileName
          : `${fileName}.${fileExtension}`;

        // Store file data for download
        setDownloadFile({
          blob: blob,
          fileName: finalFileName,
          size: blob.size,
          type: contentType
        });

        console.log('File received successfully:', finalFileName);
        setSuccess(true);
      } else {
        // Handle regular JSON response
        const result = await response.json();
        console.log('Workflow started successfully:', result);
        setSuccess(true);
      }

      // Optional: reset form after successful submission
      // resetForm();
    } catch (error) {
      console.error('Error submitting form:', error);
      setError('Failed to start workflow. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="workflow-form-container">
      <h2>Start New Workflow</h2>
      
      {success && (
        <div className="success-message">
          {downloadFile ? (
            <div className="download-section">
              <div className="success-text">
                ✅ Workflow completed successfully! Your document is ready for download.
              </div>
              <div className="file-info">
                <div className="file-details">
                  <span className="file-name">📄 {downloadFile.fileName}</span>
                  <span className="file-size">({(downloadFile.size / 1024).toFixed(1)} KB)</span>
                </div>
                <button
                  type="button"
                  className="download-btn"
                  onClick={handleDownload}
                >
                  📥 Download Document
                </button>
              </div>
            </div>
          ) : (
            <div>
              ✅ Workflow started successfully! You can monitor progress below.
            </div>
          )}
        </div>
      )}
      
      <form id="workflowForm" onSubmit={handleSubmit}>
        {/* Tab Navigation */}
        <div className="tab-navigation">
          <button
            type="button"
            className={`tab-button ${activeTab === 'basic' ? 'active' : ''}`}
            onClick={() => setActiveTab('basic')}
          >
            Basic Settings
          </button>
          <button
            type="button"
            className={`tab-button ${activeTab === 'advanced' ? 'active' : ''}`}
            onClick={() => setActiveTab('advanced')}
          >
            Advanced Options
          </button>
        </div>

        {/* Basic Settings Tab */}
        {activeTab === 'basic' && (
          <div className="tab-content">
            <div className="form-group">
              <label htmlFor="chosenTopic">Chosen Topic *</label>
              <input
                type="text"
                id="chosenTopic"
                value={formData.chosenTopic}
                onChange={handleChange}
                required
                placeholder="What is the main topic of this article"
              />
            </div>

            <div className="form-group">
              <label htmlFor="purpose">Purpose *</label>
              <textarea
                id="purpose"
                value={formData.purpose}
                onChange={handleChange}
                required
                placeholder="Describe the purpose of your analysis..."
                rows="3"
              />
            </div>

            <div className="form-group">
              <label>Industries *</label>
              <p className="field-description">Select one or more industries that relate to your topic</p>
              <div className="industry-container">
                {industryOptions.map((industry) => (
                  <div key={industry} className="industry-item">
                    <input
                      type="checkbox"
                      id={`industry-${industry}`}
                      checked={formData.industries.includes(industry)}
                      onChange={() => handleIndustryChange(industry)}
                    />
                    <label htmlFor={`industry-${industry}`} className="checkbox-label">
                      {industry}
                    </label>
                  </div>
                ))}
              </div>

              {/* Custom industry input - shows when "Other" is selected */}
              {formData.industries.includes('Other') && (
                <div className="custom-industry-input">
                  <label htmlFor="customIndustry">Please specify the industry:</label>
                  <input
                    type="text"
                    id="customIndustry"
                    value={formData.customIndustry}
                    onChange={handleCustomIndustryChange}
                    placeholder="Enter your custom industry..."
                    className="custom-industry-field"
                  />
                </div>
              )}

              {formData.industries.length > 0 && (
                <div className="selected-industries">
                  <strong>Selected:</strong> {formData.industries.map(industry =>
                    industry === 'Other' && formData.customIndustry.trim()
                      ? formData.customIndustry.trim()
                      : industry
                  ).join(', ')}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Advanced Options Tab */}
        {activeTab === 'advanced' && (
          <div className="tab-content">
            {/* Advanced Sub-Tab Navigation */}
            <div className="sub-tab-navigation">
              <button
                type="button"
                className={`sub-tab-button ${activeAdvancedTab === 'general' ? 'active' : ''}`}
                onClick={() => setActiveAdvancedTab('general')}
              >
                General Settings
              </button>
              <button
                type="button"
                className={`sub-tab-button ${activeAdvancedTab === 'crawling' ? 'active' : ''}`}
                onClick={() => setActiveAdvancedTab('crawling')}
              >
                Crawling Settings
              </button>
            </div>

            {/* General Settings Sub-Tab */}
            {activeAdvancedTab === 'general' && (
              <div className="sub-tab-content">
                <div className="form-group">
                  <label htmlFor="numberOfArticles">Number of Articles: {formData.numberOfArticles}</label>
                  <p className="field-description">Choose how many articles to generate (10-30)</p>
                  <div className="slider-container">
                    <input
                      type="range"
                      id="numberOfArticles"
                      min="10"
                      max="30"
                      value={formData.numberOfArticles}
                      onChange={handleSliderChange}
                      className="slider"
                    />
                    <div className="slider-labels">
                      <span>10</span>
                      <span>20</span>
                      <span>30</span>
                    </div>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="additionalReferences">Additional References</label>
                  <p className="field-description">Enter URLs for additional reference sources (one per line)</p>
                  <textarea
                    id="additionalReferences"
                    value={formData.additionalReferences}
                    onChange={handleChange}
                    placeholder="https://example.com/article1&#10;https://example.com/article2&#10;https://example.com/article3"
                    rows="6"
                    className="references-textarea"
                  />
                  {formData.additionalReferences.trim() && (
                    <div className="url-count">
                      {formData.additionalReferences.split('\n').filter(url => url.trim()).length} URL(s) added
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Crawling Settings Sub-Tab */}
            {activeAdvancedTab === 'crawling' && (
              <div className="sub-tab-content">
                <div className="form-group">
                  <label htmlFor="searchDepth">Search Depth: {formData.searchDepth}</label>
                  <p className="field-description">How deep to crawl linked pages (0-2 levels)</p>
                  <div className="slider-container">
                    <input
                      type="range"
                      id="searchDepth"
                      min="0"
                      max="2"
                      value={formData.searchDepth}
                      onChange={handleSliderChange}
                      className="slider"
                    />
                    <div className="slider-labels">
                      <span>0</span>
                      <span>1</span>
                      <span>2</span>
                    </div>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="maxPages">Max Pages: {formData.maxPages}</label>
                  <p className="field-description">Maximum number of pages to crawl (5-15 pages)</p>
                  <div className="slider-container">
                    <input
                      type="range"
                      id="maxPages"
                      min="5"
                      max="15"
                      value={formData.maxPages}
                      onChange={handleSliderChange}
                      className="slider"
                    />
                    <div className="slider-labels">
                      <span>5</span>
                      <span>10</span>
                      <span>15</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        
        <div className="form-actions">
          <button 
            type="button" 
            className="reset-btn" 
            onClick={resetForm}
            disabled={isSubmitting}
          >
            Reset
          </button>
          
          <button 
            type="submit" 
            className="submit-btn" 
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Starting...' : 'Start Workflow'}
          </button>
        </div>
        
        {error && <div className="error-message">{error}</div>}
      </form>
    </div>
  );
}

export default WorkflowForm;
