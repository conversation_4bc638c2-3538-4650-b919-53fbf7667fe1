import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import './ProgressMonitor.css';

const ProgressMonitor = forwardRef((props, ref) => {
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('Waiting for workflow...');
  const [status, setStatus] = useState('info');
  const [isConnected, setIsConnected] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(null);
  const pollingIntervalRef = useRef(null);

  // Fetch progress updates from REST API
  const fetchProgress = async () => {
    try {
      const response = await fetch('/api/progress/status');
      if (response.ok) {
        const data = await response.json();

        // Validate and update progress data
        if (data && typeof data === 'object') {
          console.log('� Progress update received:', data);

          // Update progress if valid number provided
          if (typeof data.progress === 'number' && data.progress >= 0 && data.progress <= 100) {
            setProgress(data.progress);
          }

          // Update message if provided
          if (data.message && typeof data.message === 'string') {
            setMessage(data.message);
          }

          // Update status if provided
          if (['info', 'success', 'warning', 'error'].includes(data.status)) {
            setStatus(data.status);
          }

          setLastUpdate(new Date().toISOString());
          setIsConnected(true);

          console.log(`📊 Progress updated: ${data.progress}% - ${data.message} (${data.status})`);
        }
      } else {
        console.warn('⚠️ Failed to fetch progress:', response.status);
        setIsConnected(false);
      }
    } catch (error) {
      console.error('❌ Error fetching progress:', error);
      setIsConnected(false);
    }
  };

  // Reset function to clear progress
  const resetProgress = async () => {
    setProgress(0);
    setMessage('Waiting for workflow...');
    setStatus('info');
    setLastUpdate(null);
    console.log('Progress monitor reset');

    // Also reset server-side progress state
    try {
      await fetch('/api/progress/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      console.log('Server progress state reset');
    } catch (error) {
      console.error('Failed to reset server progress state:', error);
    }
  };

  // Expose reset function to parent components
  useImperativeHandle(ref, () => ({
    reset: resetProgress
  }));

  // Set up polling for progress updates
  useEffect(() => {
    // Initial fetch
    fetchProgress();

    // Set up polling interval (every 2 seconds)
    pollingIntervalRef.current = setInterval(fetchProgress, 2000);

    // Cleanup interval on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  return (
    <div id="progressSection" className="progress-section active">
      <h2>Progress Monitor</h2>
      
      <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
        {isConnected
          ? '✅ Connected - Monitoring progress via API'
          : '❌ Disconnected - Unable to fetch progress updates'}
      </div>

      {lastUpdate && (
        <div className="last-update">
          Last updated: {new Date(lastUpdate).toLocaleTimeString()}
        </div>
      )}
      
      <div className="progress-container">
        <div className="progress-bar">
          <div
            id="progressFill"
            className={`progress-fill progress-${status}`}
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        <div id="progressText" className="progress-text">
          <span className="progress-percentage">{progress}%</span>
          <span className="progress-message">{message}</span>
        </div>
      </div>

      <div id="statusMessage" className={`status-message status-${status}`}>
        <div className="status-indicator">
          {status === 'info' && '🔄'}
          {status === 'success' && '✅'}
          {status === 'warning' && '⚠️'}
          {status === 'error' && '❌'}
        </div>
        <div className="status-text">{message}</div>
      </div>
      
      <div className="instructions">
        <h3>📋 How to Use:</h3>
        <p><strong>1.</strong> Fill out and submit the form above</p>
        <p><strong>2.</strong> Keep this page open to monitor progress</p>
        <p><strong>3.</strong> Watch real-time updates as your workflow runs</p>
      </div>
    </div>
  );
});

export default ProgressMonitor;
