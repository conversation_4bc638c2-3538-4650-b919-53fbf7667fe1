{"name": "n8n-frontend", "version": "1.0.0", "description": "Frontend for n8n workflow with real-time progress updates", "scripts": {"start": "PORT=3000 react-scripts start", "dev:server": "nodemon server.js --watch server.js --watch api", "dev": "concurrently \"npm run dev:server\" \"npm run start\"", "test": "react-scripts test", "lint": "eslint src", "format": "prettier --write \"src/**/*.{js,jsx,css}\""}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "ws": "^8.14.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "concurrently": "^8.0.1", "eslint": "^8.38.0", "nodemon": "^3.0.1", "prettier": "^2.8.8"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "engines": {"node": ">=18.0.0"}, "proxy": "http://localhost:80", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}