# Development-only Frontend Dockerfile
FROM node:18-alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm install

# Set development environment
ENV NODE_ENV=development
ENV PORT=3000
ENV API_PORT=80

# Expose ports
EXPOSE 3000
EXPOSE 80

# Start the application in development mode with hot reloading
CMD ["npm", "run", "dev"]
