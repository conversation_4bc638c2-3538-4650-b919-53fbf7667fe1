# React Frontend Development Guide

## Overview

This is a React-based frontend application that provides a user interface for n8n workflows with real-time progress monitoring. The application has been migrated from native HTML/JS to React for better maintainability and development experience.

## Docker Development (Recommended)

The frontend runs as a containerized application with full development features including hot reload.

### Quick Start
```bash
# From the project root directory
docker compose up

# Or run just the frontend service
docker compose up frontend
```

**Access Points:**
- **Frontend UI**: http://localhost:3000 (React app with hot reload)
- **API Server**: http://localhost:8081 (Express server)
- **Health Check**: http://localhost:8081/health

### Development Features
- **Hot Reload**: React development server with live reloading
- **API Integration**: Express server for backend communication
- **Volume Mounting**: Source code is mounted for live editing
- **Container Networking**: Seamless communication between services
- **Consistent Environment**: Same setup for development and production

## Local Development (Alternative)

If you prefer to run without Docker:

```bash
cd frontend
npm install
npm run dev  # Runs both React dev server and Express API
```

**Note**: Local development requires Node.js 18+ and may have environment inconsistencies.

## Available Scripts

- `npm start` - Start React development server (port 3000)
- `npm run dev:server` - Start Express API server with nodemon (port 3001)
- `npm run dev` - Start both React and Express servers concurrently
- `npm test` - Run React tests
- `npm run lint` - Run ESLint on source code
- `npm run format` - Format code with Prettier

## Configuration

The frontend automatically configures itself based on environment variables:
- `N8N_WEBHOOK_URL` - URL for n8n webhook (set in docker-compose)
- `N8N_WEBHOOK_TEST_URL` - URL for n8n test webhook
- `PORT` - React development server port (default: 3000)
- `API_PORT` - Express API server port (default: 3001)
- `NODE_ENV` - Environment mode (development/production)
- `CHOKIDAR_USEPOLLING` - Enable polling for hot reload in Docker

## File Structure

```
frontend/
├── src/
│   ├── components/         # React components
│   │   ├── WorkflowForm.js    # Main form component
│   │   ├── ProgressMonitor.js # Progress monitoring component
│   │   ├── LoadingSpinner.js  # Loading indicator
│   │   └── ErrorBoundary.js   # Error handling component
│   ├── hooks/
│   │   └── useWebSocket.js    # WebSocket custom hook
│   ├── App.js             # Main React application
│   ├── index.js           # React entry point
│   └── *.css              # Component styles
├── public/
│   ├── index.html         # React HTML template
│   ├── manifest.json      # PWA manifest
│   └── favicon.ico        # Application icon
├── server.js              # Express server with WebSocket support
├── package.json           # Dependencies and scripts
├── Dockerfile             # Container configuration
└── README.md             # This file
```

## Features

- **React Architecture**: Modern React 18 with hooks and functional components
- **Real-time Progress**: WebSocket connection for live workflow updates
- **Form Validation**: Client-side validation for workflow parameters
- **Responsive Design**: Works on desktop and mobile
- **Health Monitoring**: Built-in health check endpoint
- **Auto-reconnect**: WebSocket automatically reconnects if connection drops
- **Error Boundaries**: Graceful error handling with React error boundaries
- **Hot Reloading**: Development server with instant updates

## Troubleshooting

### Container won't start
```bash
# Check logs
docker compose logs frontend

# Rebuild container
docker compose build frontend
docker compose up frontend
```

### React development server issues
```bash
# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check for port conflicts
lsof -i :3000
lsof -i :3001
```

### Can't connect to n8n
- Ensure n8n service is running: `docker compose up n8n`
- Check network connectivity between containers
- Verify webhook URL in environment variables
- Check if n8n is accessible at http://localhost:5678

### WebSocket connection issues
- Check if ports 3000 and 3001 are available
- Verify firewall settings
- Check browser console for connection errors
- Ensure Express server is running on port 3001

### ESLint/Prettier issues
- ESLint configuration is included in package.json
- Run `npm run lint` to check for code issues
- Run `npm run format` to auto-format code
