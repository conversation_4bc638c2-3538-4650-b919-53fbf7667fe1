const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const cors = require('cors');

// Create Express app
const app = express();
// Use port 80 for backend communication, but also listen on 8081 for frontend proxy
const PORT = process.env.API_PORT || 80;

// Enhanced logging middleware
app.use((req, res, next) => {
  console.log(`[API Server] ${req.method} ${req.url}`);
  next();
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// API endpoints
app.get('/api/config', (req, res) => {
  // Convert container URLs to browser-accessible URLs
  const hostIp = process.env.HOST_IP || 'localhost';
  const externalApiPort = '8081'; // Always use 8081 for external API access

  const getBrowserUrl = (containerUrl, defaultUrl) => {
    if (!containerUrl) return defaultUrl;

    // Replace container names with host IP for browser access
    return containerUrl
      .replace('http://n8n:5678', `http://${hostIp}:5678`)
      .replace('ws://n8n:', `ws://${hostIp}:`)
      .replace('ws://localhost:80', `ws://${hostIp}:${externalApiPort}`);
  };

  const config = {
    n8nWebhookUrl: getBrowserUrl(
      process.env.N8N_WEBHOOK_TEST_URL,
      // process.env.N8N_WEBHOOK_URL,
      `http://${hostIp}:5678/webhook-test/journal`
    ),
    progressWsUrl: getBrowserUrl(
      process.env.PROGRESS_WS_URL,
      `ws://${hostIp}:${externalApiPort}/progress`
    )
  };

  console.log('Config endpoint called, returning:', config);

  res.json(config);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).send('OK');
});

// Serve static files from public directory in development
console.log('Running in development mode');
app.use(express.static(path.join(__dirname, 'public')));

// Create HTTP server
const server = http.createServer(app);

// Create WebSocket server with additional options
const wss = new WebSocket.Server({
  server,
  path: '/progress',
  perMessageDeflate: false,
  maxPayload: 16 * 1024 * 1024, // 16MB
  verifyClient: (info) => {
    console.log(`WebSocket connection attempt from: ${info.origin || 'unknown origin'}`);
    return true; // Accept all connections in development
  }
});

// WebSocket connection handling
wss.on('connection', (ws, req) => {
  console.log(`Client connected to progress WebSocket from ${req.socket.remoteAddress}`);

  // Send initial status in the expected format
  try {
    const initialMessage = {
      progress: 0,
      message: 'Connected to progress monitor',
      status: 'info'
    };
    ws.send(JSON.stringify(initialMessage));
    console.log('📤 Sent initial WebSocket message:', initialMessage);
  } catch (error) {
    console.error('❌ Error sending initial WebSocket message:', error);
  }

  ws.on('close', (code, reason) => {
    console.log(`Client disconnected from progress WebSocket. Code: ${code}, Reason: ${reason}`);
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });

  // Send periodic heartbeat to keep connection alive
  const heartbeat = setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.ping();
    } else {
      clearInterval(heartbeat);
    }
  }, 30000); // 30 seconds

  ws.on('pong', () => {
    console.log('WebSocket pong received');
  });
});

// Store current progress state
let currentProgress = {
  progress: 0,
  message: 'Waiting for workflow...',
  status: 'info',
  lastUpdated: new Date().toISOString()
};

// Progress update endpoint (for backend to send updates)
app.post('/api/progress', (req, res) => {
  const update = req.body;
  console.log('📨 Progress update received:', update);

  // Validate and store the progress update
  if (update && typeof update === 'object') {
    // Update progress if valid number provided
    if (typeof update.progress === 'number' && update.progress >= 0 && update.progress <= 100) {
      currentProgress.progress = update.progress;
    }

    // Update message if provided
    if (update.message && typeof update.message === 'string') {
      currentProgress.message = update.message;
    }

    // Update status if provided
    if (['info', 'success', 'warning', 'error'].includes(update.status)) {
      currentProgress.status = update.status;
    }

    currentProgress.lastUpdated = new Date().toISOString();

    console.log('📊 Progress state updated:', currentProgress);

    // Also broadcast to WebSocket clients if any are connected (for backward compatibility)
    wss.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(currentProgress));
      }
    });

    res.status(200).json({
      success: true,
      message: 'Progress update received',
      currentProgress
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'Invalid progress update format'
    });
  }
});

// Progress status endpoint (for frontend to poll current status)
app.get('/api/progress/status', (req, res) => {
  console.log('📤 Progress status requested, returning:', currentProgress);
  res.json(currentProgress);
});

// Progress reset endpoint (for frontend to reset progress)
app.post('/api/progress/reset', (req, res) => {
  console.log('🔄 Progress reset requested');
  currentProgress = {
    progress: 0,
    message: 'Waiting for workflow...',
    status: 'info',
    lastUpdated: new Date().toISOString()
  };
  console.log('✅ Progress reset completed:', currentProgress);
  res.json({ success: true, message: 'Progress reset successfully' });
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Express server running on port ${PORT}`);
  console.log(`🔌 WebSocket server running on ws://localhost:${PORT}/progress`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📁 Current directory: ${__dirname}`);
  console.log(`📡 Backend can send progress updates to: http://frontend:${PORT}/api/progress`);
});
